const {OpenAI} = require('openai');
const functions = require('firebase-functions');

// OpenAI instance (lazy-loaded singleton)
let openai;

// OpenAI model configurations
const OPENAI_MODELS = {
  // Fast model for simple tasks
  FAST: 'gpt-4o-mini',
  // Standard model for most tasks
  STANDARD: 'gpt-4',
  // High-quality model for complex tasks
  PREMIUM: 'gpt-4',
};

// Default configurations for different use cases
const OPENAI_CONFIGS = {
  MEAL_PLAN: {
    model: OPENAI_MODELS.FAST,
    temperature: 0.3,
    max_tokens: 4000,
  },
  SHOPPING_LIST: {
    model: OPENAI_MODELS.FAST,
    temperature: 0.7,
    max_tokens: 2000,
  },
  NUTRITION_ANALYSIS: {
    model: OPENAI_MODELS.FAST,
    temperature: 0.2,
    max_tokens: 1500,
  },
  MEAL_REPLACEMENT: {
    model: OPENAI_MODELS.FAST,
    temperature: 0.5,
    max_tokens: 3000,
  },
};

function getOpenAI() {
  if (!openai) {
    const apiKey = process.env.OPENAI_API_KEY ||
      (functions.config().openai && functions.config().openai.api_key);

    if (!apiKey) {
      throw new Error('OpenAI API key not found in environment variables or Firebase config');
    }

    openai = new OpenAI({apiKey});
    console.log('OpenAI client initialized');
  }
  return openai;
}

module.exports = {
  getOpenAI,
  OPENAI_MODELS,
  OPENAI_CONFIGS,
};
