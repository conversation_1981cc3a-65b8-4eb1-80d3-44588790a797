const functions = require('firebase-functions');
const express = require('express');
const cors = require('cors');

// Initialize Firebase
const {initializeFirebase} = require('./config/firebase');
initializeFirebase();

// Import routes
const mealPlanRoutes = require('./routes/mealPlan');
const nutritionRoutes = require('./routes/nutrition');
const userRoutes = require('./routes/user');

// Import triggers
const {onUserCreated, onUserDeleted} = require('./triggers');

// Initialize Express app with CORS
const app = express();
app.use(cors({origin: true}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'easydietai-functions',
  });
});

// API routes
app.use('/', mealPlanRoutes);
app.use('/', nutritionRoutes);
app.use('/', userRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    details: error instanceof Error ? error.message : 'Unknown error',
  });
});

// Export the Express app as a Firebase Function
exports.api = functions.https.onRequest(app);

// Export Firebase Auth triggers
exports.onUserCreated = onUserCreated;
exports.onUserDeleted = onUserDeleted;
