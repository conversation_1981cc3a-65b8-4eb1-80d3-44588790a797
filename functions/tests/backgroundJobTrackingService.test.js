const {
  COLLECTIONS,
  JOB_STATUS,
  checkAndStartBackgroundJob,
  completeBackgroundJob,
  getBackgroundJobStatus,
  validateDaysGenerating,
  validateMealImageGenerating,
  validateMealReplacement
} = require('../services/backgroundJobTrackingService');

// Mock Firebase
jest.mock('../config/firebase', () => ({
  initializeFirebase: jest.fn(() => ({
    firestore: jest.fn(() => ({
      collection: jest.fn(() => ({
        doc: jest.fn(() => ({
          collection: jest.fn(() => ({
            doc: jest.fn(() => ({
              get: jest.fn(),
              set: jest.fn(),
              update: jest.fn()
            }))
          }))
        }))
      }))
    }))
  }))
}));

// Mock Firestore Timestamp
jest.mock('firebase-admin/firestore', () => ({
  Timestamp: {
    now: jest.fn(() => ({
      toDate: jest.fn(() => new Date('2024-01-15T10:00:00Z'))
    })),
    fromDate: jest.fn((date) => ({
      toDate: jest.fn(() => date)
    }))
  }
}));

describe('Background Job Tracking Service', () => {
  const mockUserId = 'test-user-123';
  const mockMealId = 'meal-456';
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('COLLECTIONS constant', () => {
    test('should have correct collection names with bjt_ prefix', () => {
      expect(COLLECTIONS.SHOPPING_LIST).toBe('bjt_shopping_list');
      expect(COLLECTIONS.DAYS_GENERATING).toBe('bjt_days_generating');
      expect(COLLECTIONS.MEAL_REPLACEMENT).toBe('bjt_meal_replacement');
      expect(COLLECTIONS.MEAL_IMAGE_GENERATING).toBe('bjt_meal_image_generating');
    });
  });

  describe('JOB_STATUS constant', () => {
    test('should have correct status values', () => {
      expect(JOB_STATUS.IN_PROGRESS).toBe('in-progress');
      expect(JOB_STATUS.SUCCESS).toBe('success');
      expect(JOB_STATUS.FAIL).toBe('fail');
    });
  });

  describe('checkAndStartBackgroundJob', () => {
    test('should create new job document when none exists', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({ exists: false }),
        set: jest.fn().mockResolvedValue()
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      await checkAndStartBackgroundJob(
        mockUserId,
        COLLECTIONS.MEAL_IMAGE_GENERATING,
        mockMealId,
        {},
        3,
        'إنشاء صورة الوجبة'
      );

      expect(mockDocRef.set).toHaveBeenCalledWith({
        trigger_count: 1,
        status: JOB_STATUS.IN_PROGRESS,
        status_message: '',
        last_trigger_datetime: expect.any(Object),
        last_completion_datetime: null
      });
    });

    test('should increment trigger_count when job exists and not in progress', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => ({
            trigger_count: 1,
            status: JOB_STATUS.SUCCESS
          })
        }),
        update: jest.fn().mockResolvedValue()
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      await checkAndStartBackgroundJob(
        mockUserId,
        COLLECTIONS.MEAL_IMAGE_GENERATING,
        mockMealId,
        {},
        3,
        'إنشاء صورة الوجبة'
      );

      expect(mockDocRef.update).toHaveBeenCalledWith({
        trigger_count: 2,
        status: JOB_STATUS.IN_PROGRESS,
        status_message: '',
        last_trigger_datetime: expect.any(Object)
      });
    });

    test('should throw error when job is in progress', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => ({
            trigger_count: 1,
            status: JOB_STATUS.IN_PROGRESS
          })
        })
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      await expect(checkAndStartBackgroundJob(
        mockUserId,
        COLLECTIONS.MEAL_IMAGE_GENERATING,
        mockMealId,
        {},
        3,
        'إنشاء صورة الوجبة'
      )).rejects.toThrow('يوجد عملية إنشاء صورة الوجبة قيد التنفيذ حالياً');
    });

    test('should throw error when max attempts exceeded', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => ({
            trigger_count: 3,
            status: JOB_STATUS.FAIL
          })
        })
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      await expect(checkAndStartBackgroundJob(
        mockUserId,
        COLLECTIONS.MEAL_IMAGE_GENERATING,
        mockMealId,
        {},
        3,
        'إنشاء صورة الوجبة'
      )).rejects.toThrow('تم الوصول للحد الأقصى من المحاولات (3)');
    });

    test('should include meta data when provided', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({ exists: false }),
        set: jest.fn().mockResolvedValue()
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const metaData = {
        from_date: new Date('2024-01-15'),
        days_count: 7
      };

      await checkAndStartBackgroundJob(
        mockUserId,
        COLLECTIONS.DAYS_GENERATING,
        'it',
        metaData,
        1,
        'إنشاء خطة الوجبات'
      );

      expect(mockDocRef.set).toHaveBeenCalledWith({
        trigger_count: 1,
        status: JOB_STATUS.IN_PROGRESS,
        status_message: '',
        last_trigger_datetime: expect.any(Object),
        last_completion_datetime: null,
        meta: metaData
      });
    });
  });

  describe('completeBackgroundJob', () => {
    test('should update job status to success', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({ exists: true }),
        update: jest.fn().mockResolvedValue()
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      await completeBackgroundJob(
        mockUserId,
        COLLECTIONS.MEAL_IMAGE_GENERATING,
        mockMealId,
        JOB_STATUS.SUCCESS
      );

      expect(mockDocRef.update).toHaveBeenCalledWith({
        status: JOB_STATUS.SUCCESS,
        status_message: '',
        last_completion_datetime: expect.any(Object)
      });
    });

    test('should update job status to fail with message', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({ exists: true }),
        update: jest.fn().mockResolvedValue()
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const errorMessage = 'حدث خطأ أثناء إنشاء الصورة';
      await completeBackgroundJob(
        mockUserId,
        COLLECTIONS.MEAL_IMAGE_GENERATING,
        mockMealId,
        JOB_STATUS.FAIL,
        errorMessage
      );

      expect(mockDocRef.update).toHaveBeenCalledWith({
        status: JOB_STATUS.FAIL,
        status_message: errorMessage,
        last_completion_datetime: expect.any(Object)
      });
    });
  });

  describe('getBackgroundJobStatus', () => {
    test('should return job data when document exists', async () => {
      const mockJobData = {
        trigger_count: 2,
        status: JOB_STATUS.SUCCESS,
        last_completion_datetime: new Date()
      };

      const mockDocRef = {
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => mockJobData
        })
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const result = await getBackgroundJobStatus(
        mockUserId,
        COLLECTIONS.MEAL_IMAGE_GENERATING,
        mockMealId
      );

      expect(result).toEqual(mockJobData);
    });

    test('should return null when document does not exist', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({ exists: false })
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const result = await getBackgroundJobStatus(
        mockUserId,
        COLLECTIONS.MEAL_IMAGE_GENERATING,
        mockMealId
      );

      expect(result).toBeNull();
    });
  });

  describe('validateMealImageGenerating', () => {
    test('should pass validation when no existing job', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({ exists: false })
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const result = await validateMealImageGenerating(mockUserId, mockMealId);

      expect(result.isValid).toBe(true);
    });

    test('should fail validation when max attempts reached', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => ({
            trigger_count: 3,
            status: JOB_STATUS.FAIL
          })
        })
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const result = await validateMealImageGenerating(mockUserId, mockMealId);

      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('تم الوصول للحد الأقصى من المحاولات (3)');
      expect(result.triggerCount).toBe(3);
    });

    test('should pass validation when under max attempts', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => ({
            trigger_count: 2,
            status: JOB_STATUS.FAIL
          })
        })
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const result = await validateMealImageGenerating(mockUserId, mockMealId);

      expect(result.isValid).toBe(true);
    });
  });

  describe('validateMealReplacement', () => {
    test('should pass validation when no existing job', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({ exists: false })
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const result = await validateMealReplacement(mockUserId, mockMealId);

      expect(result.isValid).toBe(true);
    });

    test('should fail validation when max attempts reached', async () => {
      const mockDocRef = {
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => ({
            trigger_count: 3,
            status: JOB_STATUS.FAIL
          })
        })
      };

      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              doc: jest.fn(() => mockDocRef)
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const result = await validateMealReplacement(mockUserId, mockMealId);

      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('تم الوصول للحد الأقصى من المحاولات (3)');
      expect(result.triggerCount).toBe(3);
    });
  });

  describe('validateDaysGenerating', () => {
    test('should pass validation when no existing meals', async () => {
      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              orderBy: jest.fn(() => ({
                limit: jest.fn(() => ({
                  get: jest.fn().mockResolvedValue({ empty: true })
                }))
              }))
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const result = await validateDaysGenerating(mockUserId);

      expect(result.isValid).toBe(true);
    });

    test('should handle validation logic correctly', async () => {
      // Test that the validation function exists and can be called
      // The actual date logic is tested in integration tests
      const mockFirestore = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            collection: jest.fn(() => ({
              orderBy: jest.fn(() => ({
                limit: jest.fn(() => ({
                  get: jest.fn().mockResolvedValue({ empty: true })
                }))
              }))
            }))
          }))
        }))
      };

      require('../config/firebase').initializeFirebase.mockReturnValue({
        firestore: () => mockFirestore
      });

      const result = await validateDaysGenerating(mockUserId);

      expect(result).toHaveProperty('isValid');
      expect(typeof result.isValid).toBe('boolean');
    });
  });
});
