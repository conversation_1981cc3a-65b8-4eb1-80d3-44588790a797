/**
 * Custom error class for API responses
 */
class ApiError extends Error {
  constructor(message, statusCode = 500, errorCode = null, details = null) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.details = details;
    this.name = 'ApiError';
  }
}

/**
 * Response handler decorator that wraps route handlers
 * Handles validation, response formatting and error handling in one place
 * @param {Function} handler - The route handler function
 * @param {Object} validationSchema - Optional Joi validation schema for request body
 * @return {Function} Wrapped handler function
 */
function withResponseHandler(handler, validationSchema = null) {
  return async (req, res) => {
    try {
      let validatedData = req.body;

      // Perform validation if schema is provided
      if (validationSchema) {
        const {error, value} = validationSchema.validate(req.body);
        if (error) {
          throw createApiError(error.details[0].message, 400, 'VALIDATION_ERROR');
        }
        validatedData = value;
      }

      // Call the actual handler function with validated data
      const result = await handler(req, res, validatedData);

      // If handler already sent response (like for background processing), don't send again
      if (res.headersSent) {
        return;
      }

      // If result is null/undefined, send default success
      if (result === null || result === undefined) {
        return res.status(200).json({
          success: true,
        });
      }

      // If result has statusCode, use it, otherwise default to 200
      const statusCode = result.statusCode || 200;

      // Format successful response
      const response = {
        success: true,
        ...result,
      };

      // Remove statusCode from response body if it exists
      delete response.statusCode;

      return res.status(statusCode).json(response);
    } catch (error) {
      console.error('Route handler error:', error);

      // Handle ApiError instances
      if (error instanceof ApiError) {
        return res.status(error.statusCode).json({
          success: false,
          error: error.message,
          ...(error.errorCode && {error_code: error.errorCode}),
          ...(error.details && {details: error.details}),
        });
      }

      // Handle service errors with custom codes
      if (error.code) {
        const statusCode = getStatusCodeForErrorCode(error.code);
        const response = {
          success: false,
          error: error.message,
          error_code: error.code,
          ...(error.details && {details: error.details}),
        };

        // Add additional error-specific data
        if (error.availableDateRange) {
          response.available_date_range = error.availableDateRange;
        }

        return res.status(statusCode).json(response);
      }

      // Handle generic errors
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };
}

/**
 * Map error codes to HTTP status codes
 * @param {string} errorCode - Error code from service
 * @return {number} HTTP status code
 */
function getStatusCodeForErrorCode(errorCode) {
  const errorCodeMap = {
    'NO_MEALS_FOUND': 404,
    'VALIDATION_ERROR': 400,
    'INVALID_DATE_RANGE': 400,
    'INVALID_PARAMETERS': 400,
    'USER_NOT_FOUND': 404,
    'SHOPPING_LIST_NOT_FOUND': 404,
    'REPLACEMENT_LIMIT_EXCEEDED': 429,
    'BACKGROUND_JOB_IN_PROGRESS': 429,
    'MEAL_PLAN_GENERATION_BLOCKED': 400,
    'UNAUTHORIZED': 401,
    'FORBIDDEN': 403,
    'RATE_LIMIT_EXCEEDED': 429,
    'EXTERNAL_API_ERROR': 502,
    'DATABASE_ERROR': 500,
  };

  return errorCodeMap[errorCode] || 500;
}

/**
 * Helper function to create API errors
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code
 * @param {string} errorCode - Custom error code
 * @param {any} details - Additional error details
 * @return {ApiError} API error instance
 */
function createApiError(message, statusCode = 500, errorCode = null, details = null) {
  return new ApiError(message, statusCode, errorCode, details);
}

/**
 * Helper function to create success responses
 * @param {any} data - Response data
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code (default: 200)
 * @return {Object} Success response object
 */
function createSuccessResponse(data = null, message = null, statusCode = 200) {
  const response = {
    statusCode,
    ...(message && {message}),
    ...(data && (typeof data === 'object' ? data : {data})),
  };

  return response;
}

module.exports = {
  withResponseHandler,
  ApiError,
  createApiError,
  createSuccessResponse,
};
