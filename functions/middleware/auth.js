const {initializeFirebase} = require('../config/firebase');

/**
 * Middleware for authenticating Firebase users
 * Verifies the Firebase ID token and adds userId to request body
 */
const authenticateUser = async (req, res, next) => {
  try {
    const admin = initializeFirebase();
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized: No valid token provided',
      });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);

    // Add user ID to request body for downstream use
    req.body.userId = decodedToken.uid;
    req.user = decodedToken; // Add full user info if needed

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({
      error: 'Unauthorized: Invalid token',
    });
  }
};

module.exports = {
  authenticateUser,
};
