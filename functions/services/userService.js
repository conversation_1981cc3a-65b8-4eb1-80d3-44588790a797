const {initializeFirebase} = require('../config/firebase');

/**
 * Creates a new user document in Firestore
 * @param {Object} user - Firebase user object
 * @returns {Object} Created user document reference
 */
async function createUserDocument(user) {
  try {
    const admin = initializeFirebase();
    const userDoc = {
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      isPremium: false,
      onboardingCompleted: false,
    };

    const docRef = await admin.firestore()
      .collection('users')
      .doc(user.uid)
      .set(userDoc);

    console.log('User document created for:', user.uid);
    return docRef;
  } catch (error) {
    console.error('Error creating user document:', error);
    throw error;
  }
}

/**
 * Deletes all user data from Firestore
 * @param {string} userId - User ID to delete
 * @returns {void}
 */
async function deleteUserData(userId) {
  try {
    const admin = initializeFirebase();
    const batch = admin.firestore().batch();

    // Delete user document
    const userDocRef = admin.firestore().collection('users').doc(userId);
    batch.delete(userDocRef);

    // Delete user's meal plans
    const mealPlansSnapshot = await admin.firestore()
      .collection('meal_plans')
      .where('userId', '==', userId)
      .get();

    mealPlansSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    // Delete user's nutrition analyses (if we add this collection later)
    const nutritionSnapshot = await admin.firestore()
      .collection('nutrition_analyses')
      .where('userId', '==', userId)
      .get();

    nutritionSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log('User data deleted for:', userId);
  } catch (error) {
    console.error('Error deleting user data:', error);
    throw error;
  }
}

/**
 * Gets user profile from Firestore
 * @param {string} userId - User ID
 * @returns {Object|null} User profile data or null if not found
 */
async function getUserProfile(userId) {
  try {
    const admin = initializeFirebase();
    const userDoc = await admin.firestore()
      .collection('users')
      .doc(userId)
      .get();

    if (!userDoc.exists) {
      return null;
    }

    return {
      id: userDoc.id,
      ...userDoc.data(),
    };
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
}

/**
 * Updates user profile in Firestore
 * @param {string} userId - User ID
 * @param {Object} updates - Fields to update
 * @returns {Object} Update result
 */
async function updateUserProfile(userId, updates) {
  try {
    const admin = initializeFirebase();
    const updateData = {
      ...updates,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await admin.firestore()
      .collection('users')
      .doc(userId)
      .update(updateData);

    console.log('User profile updated for:', userId);
    return {success: true};
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
}

module.exports = {
  createUserDocument,
  deleteUserData,
  getUserProfile,
  updateUserProfile,
};
