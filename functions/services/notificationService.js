const {initializeFirebase} = require('../config/firebase');
const {Timestamp} = require('firebase-admin/firestore');

/**
 * Generic notification types with their content generators
 */
const NOTIFICATION_TYPES = {
  SHOPPING_LIST_READY: 'shopping_list_ready',
  MEAL_PLAN_READY: 'meal_plan_ready',
  MEAL_REPLACEMENT_READY: 'meal_replacement_ready',
  MEAL_IMAGE_READY: 'meal_image_ready',
  WATER_REMINDER: 'water_reminder',
  CUSTOM: 'custom',
};

/**
 * Create a notification for a user
 * @param {string} userId - User ID
 * @param {Object} notificationData - Notification data
 * @param {string} notificationData.type - Notification type from NOTIFICATION_TYPES
 * @param {Object} notificationData.content - Content specific to notification type
 * @param {Object} notificationData.action - Optional action object for navigation
 * @param {string} notificationData.customId - Optional custom ID prefix
 * @return {Object} Created notification data
 */
async function createNotification(userId, notificationData) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Get user profile to determine preferred language
    const userDoc = await db.collection('users').doc(userId).get();
    const userData = userDoc.data();
    const preferredLanguage = userData?.preferred_language || 'ar';

    // Generate notification content based on type and language
    const notificationContent = generateNotificationContent(
      notificationData.type,
      notificationData.content,
      preferredLanguage
    );

    // Generate unique notification ID
    const notificationId = generateNotificationId(notificationData.type, notificationData.customId);

    // Create notification document
    const notification = {
      id: notificationId,
      title: notificationContent.title,
      message: notificationContent.message,
      timestamp: Timestamp.now(),
      seen: false,
      type: notificationData.type,
      ...(notificationData.action && { action: notificationData.action }),
    };

    // Add notification to user's notifications history subcollection
    await db.collection('users').doc(userId).collection('notifications').doc('history').collection('items').doc(notificationId).set(notification);

    // Update notification metadata
    await updateNotificationMetadata(userId);

    console.log('Notification created successfully for user:', userId, 'Type:', notificationData.type);

    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Generate notification content based on type and language
 * @param {string} type - Notification type
 * @param {Object} content - Content data specific to notification type
 * @param {string} preferredLanguage - User's preferred language
 * @return {Object} Generated notification content with title and message
 */
function generateNotificationContent(type, content, preferredLanguage) {
  const isArabic = preferredLanguage === 'ar';

  switch (type) {
    case NOTIFICATION_TYPES.SHOPPING_LIST_READY:
      return generateShoppingListContent(content, isArabic);

    case NOTIFICATION_TYPES.MEAL_PLAN_READY:
      return generateMealPlanContent(content, isArabic);

    case NOTIFICATION_TYPES.MEAL_REPLACEMENT_READY:
      return generateMealReplacementContent(content, isArabic);

    case NOTIFICATION_TYPES.MEAL_IMAGE_READY:
      return generateMealImageContent(content, isArabic);

    case NOTIFICATION_TYPES.WATER_REMINDER:
      return generateWaterReminderContent(content, isArabic);

    case NOTIFICATION_TYPES.CUSTOM:
      return {
        title: content.title || (isArabic ? 'إشعار' : 'Notification'),
        message: content.message || (isArabic ? 'لديك إشعار جديد' : 'You have a new notification'),
      };
    
    default:
      return {
        title: isArabic ? 'إشعار' : 'Notification',
        message: isArabic ? 'لديك إشعار جديد' : 'You have a new notification',
      };
  }
}

/**
 * Generate shopping list notification content
 * @param {Object} content - Shopping list content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateShoppingListContent(content, isArabic) {
  const { title, dateRange } = content;
  
  if (isArabic) {
    return {
      title: 'قائمة التسوق جاهزة',
      message: `قائمة التسوق "${title}" للفترة ${dateRange} جاهزة! اضغط لعرض قائمة التسوق.`,
    };
  } else {
    return {
      title: 'Shopping List Ready',
      message: `Your shopping list "${title}" for ${dateRange} is ready! Tap to view your shopping list.`,
    };
  }
}

/**
 * Generate meal plan notification content
 * @param {Object} content - Meal plan content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateMealPlanContent(content, isArabic) {
  const { duration, planType } = content;

  if (isArabic) {
    return {
      title: 'خطة الوجبات جاهزة',
      message: `خطة الوجبات الخاصة بك لمدة ${duration} أيام جاهزة! اضغط لعرض خطة الوجبات.`,
    };
  } else {
    return {
      title: 'Meal Plan Ready',
      message: `Your ${duration}-day meal plan is ready! Tap to view your meal plan.`,
    };
  }
}

/**
 * Generate meal replacement notification content
 * @param {Object} content - Meal replacement content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateMealReplacementContent(content, isArabic) {
  const { mealName } = content;

  if (isArabic) {
    return {
      title: 'بديل الوجبة جاهز',
      message: `بديل الوجبة الجديد "${mealName}" جاهز! اضغط لعرض خيارات الوجبة.`,
    };
  } else {
    return {
      title: 'Meal Replacement Ready',
      message: `Your new meal replacement "${mealName}" is ready! Tap to view your meal options.`,
    };
  }
}

/**
 * Generate meal image notification content
 * @param {Object} content - Meal image content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateMealImageContent(content, isArabic) {
  const { mealName } = content;

  if (isArabic) {
    return {
      title: 'صورة الوجبة جاهزة',
      message: `تم إنشاء صورة للوجبة "${mealName}" بنجاح! اضغط لعرض الصورة.`,
    };
  } else {
    return {
      title: 'Meal Image Ready',
      message: `Image for "${mealName}" has been generated successfully! Tap to view the image.`,
    };
  }
}

/**
 * Generate water reminder notification content
 * @param {Object} content - Water reminder content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateWaterReminderContent(content, isArabic) {
  const { targetAmount, currentAmount } = content;
  
  if (isArabic) {
    return {
      title: 'تذكير شرب الماء',
      message: `حان وقت شرب الماء! لقد شربت ${currentAmount || 0} من ${targetAmount} أكواب اليوم.`,
    };
  } else {
    return {
      title: 'Water Reminder',
      message: `Time to drink water! You've had ${currentAmount || 0} of ${targetAmount} glasses today.`,
    };
  }
}

/**
 * Generate unique notification ID
 * @param {string} type - Notification type
 * @param {string} customId - Optional custom ID prefix
 * @return {string} Generated notification ID
 */
function generateNotificationId(type, customId) {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substr(2, 9);
  const prefix = customId || type;
  return `${prefix}_${timestamp}_${randomSuffix}`;
}

/**
 * Update notification metadata for the user
 * @param {string} userId - User ID
 */
async function updateNotificationMetadata(userId) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Get current metadata from the notifications meta document
    const metaDocRef = db.collection('users').doc(userId).collection('notifications').doc('meta');
    const metaDoc = await metaDocRef.get();
    const currentMeta = metaDoc.exists ? metaDoc.data() : { total_count: 0, unseen_count: 0 };

    // Increment counts
    const updatedMeta = {
      total_count: (currentMeta.total_count || 0) + 1,
      unseen_count: (currentMeta.unseen_count || 0) + 1,
    };

    // Update the meta document in the notifications collection
    await metaDocRef.set(updatedMeta, { merge: true });

    console.log('Notification metadata updated for user:', userId, updatedMeta);
  } catch (error) {
    console.error('Error updating notification metadata:', error);
    throw error;
  }
}

/**
 * Format date range for notification messages
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {string} preferredLanguage - User's preferred language
 * @return {string} Formatted date range
 */
function formatDateRange(startDate, endDate, preferredLanguage) {
  // Validate input parameters
  if (!startDate || !(startDate instanceof Date)) {
    console.error('formatDateRange: Invalid startDate provided:', startDate);
    return 'Invalid date';
  }

  const options = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  };

  // If endDate is null or invalid, format only the start date
  if (!endDate || !(endDate instanceof Date)) {
    if (preferredLanguage === 'en') {
      return startDate.toLocaleDateString('en-US', options);
    } else {
      return startDate.toLocaleDateString('ar-SA', options);
    }
  }

  // Format date range with both start and end dates
  if (preferredLanguage === 'en') {
    const start = startDate.toLocaleDateString('en-US', options);
    const end = endDate.toLocaleDateString('en-US', options);
    return startDate.getTime() === endDate.getTime() ? start : `${start} - ${end}`;
  } else {
    // Arabic formatting
    const start = startDate.toLocaleDateString('ar-SA', options);
    const end = endDate.toLocaleDateString('ar-SA', options);
    return startDate.getTime() === endDate.getTime() ? start : `${start} - ${end}`;
  }
}

/**
 * Get notifications for a user with pagination
 * @param {string} userId - User ID
 * @param {number} limit - Number of notifications to fetch (default: 20)
 * @param {string} lastNotificationId - Last notification ID for pagination (optional)
 * @return {Object} Object containing notifications array and metadata
 */
async function getNotifications(userId, limit = 20, lastNotificationId = null) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Get metadata
    const metaDocRef = db.collection('users').doc(userId).collection('notifications').doc('meta');
    const metaDoc = await metaDocRef.get();
    const metadata = metaDoc.exists ? metaDoc.data() : { total_count: 0, unseen_count: 0 };

    // Build query for notifications
    let query = db.collection('users').doc(userId).collection('notifications').doc('history').collection('items')
      .orderBy('timestamp', 'desc')
      .limit(limit);

    // Add pagination if lastNotificationId is provided
    if (lastNotificationId) {
      const lastNotificationDoc = await db.collection('users').doc(userId).collection('notifications').doc('history').collection('items').doc(lastNotificationId).get();
      if (lastNotificationDoc.exists) {
        query = query.startAfter(lastNotificationDoc);
      }
    }

    // Execute query
    const snapshot = await query.get();
    const notifications = [];

    snapshot.forEach(doc => {
      notifications.push({
        id: doc.id,
        ...doc.data(),
      });
    });

    return {
      notifications,
      metadata,
      hasMore: notifications.length === limit,
      lastNotificationId: notifications.length > 0 ? notifications[notifications.length - 1].id : null,
    };
  } catch (error) {
    console.error('Error getting notifications:', error);
    throw error;
  }
}

/**
 * Mark a notification as seen
 * @param {string} userId - User ID
 * @param {string} notificationId - Notification ID
 * @return {boolean} Success status
 */
async function markNotificationAsSeen(userId, notificationId) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Get the notification document
    const notificationRef = db.collection('users').doc(userId).collection('notifications').doc('history').collection('items').doc(notificationId);
    const notificationDoc = await notificationRef.get();

    if (!notificationDoc.exists) {
      throw new Error('Notification not found');
    }

    const notificationData = notificationDoc.data();

    // Only update if notification is currently unseen
    if (!notificationData.seen) {
      // Update notification as seen
      await notificationRef.update({ seen: true });

      // Update metadata to decrement unseen count
      const metaDocRef = db.collection('users').doc(userId).collection('notifications').doc('meta');
      const metaDoc = await metaDocRef.get();
      const currentMeta = metaDoc.exists ? metaDoc.data() : { total_count: 0, unseen_count: 0 };

      const updatedMeta = {
        ...currentMeta,
        unseen_count: Math.max((currentMeta.unseen_count || 0) - 1, 0),
      };

      await metaDocRef.set(updatedMeta, { merge: true });

      console.log('Notification marked as seen for user:', userId, 'Notification ID:', notificationId);
    }

    return true;
  } catch (error) {
    console.error('Error marking notification as seen:', error);
    throw error;
  }
}

/**
 * Mark all notifications as seen for a user
 * @param {string} userId - User ID
 * @return {number} Number of notifications marked as seen
 */
async function markAllNotificationsAsSeen(userId) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Get all unseen notifications
    const unseenQuery = db.collection('users').doc(userId).collection('notifications').doc('history').collection('items')
      .where('seen', '==', false);

    const unseenSnapshot = await unseenQuery.get();
    const batch = db.batch();
    let count = 0;

    // Mark all unseen notifications as seen
    unseenSnapshot.forEach(doc => {
      batch.update(doc.ref, { seen: true });
      count++;
    });

    // Update metadata to set unseen count to 0
    const metaDocRef = db.collection('users').doc(userId).collection('notifications').doc('meta');
    batch.set(metaDocRef, { unseen_count: 0 }, { merge: true });

    // Execute batch
    await batch.commit();

    console.log('Marked all notifications as seen for user:', userId, 'Count:', count);
    return count;
  } catch (error) {
    console.error('Error marking all notifications as seen:', error);
    throw error;
  }
}

/**
 * Get notification metadata for a user
 * @param {string} userId - User ID
 * @return {Object} Notification metadata
 */
async function getNotificationMetadata(userId) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    const metaDocRef = db.collection('users').doc(userId).collection('notifications').doc('meta');
    const metaDoc = await metaDocRef.get();

    return metaDoc.exists ? metaDoc.data() : { total_count: 0, unseen_count: 0 };
  } catch (error) {
    console.error('Error getting notification metadata:', error);
    throw error;
  }
}

/**
 * Migrate existing notifications from old structure to new structure
 * This function moves notifications from users/{userId}/notifications/{notificationId}
 * to users/{userId}/notifications/history/items/{notificationId}
 * and creates the meta document with proper counts
 * @param {string} userId - User ID
 * @return {Object} Migration result with counts
 */
async function migrateUserNotifications(userId) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    console.log('Starting notification migration for user:', userId);

    // Get all existing notifications in the old structure
    const oldNotificationsRef = db.collection('users').doc(userId).collection('notifications');
    const oldNotificationsSnapshot = await oldNotificationsRef.get();

    if (oldNotificationsSnapshot.empty) {
      console.log('No notifications to migrate for user:', userId);
      return { migrated: 0, total_count: 0, unseen_count: 0 };
    }

    const batch = db.batch();
    let totalCount = 0;
    let unseenCount = 0;
    const migratedNotifications = [];

    // Process each notification
    for (const doc of oldNotificationsSnapshot.docs) {
      // Skip meta document if it already exists
      if (doc.id === 'meta' || doc.id === 'history') {
        continue;
      }

      const notificationData = doc.data();
      totalCount++;

      if (!notificationData.seen) {
        unseenCount++;
      }

      // Add to new structure
      const newNotificationRef = db.collection('users').doc(userId)
        .collection('notifications').doc('history')
        .collection('items').doc(doc.id);

      batch.set(newNotificationRef, notificationData);

      // Mark old document for deletion
      batch.delete(doc.ref);

      migratedNotifications.push(doc.id);
    }

    // Create/update meta document
    const metaDocRef = db.collection('users').doc(userId).collection('notifications').doc('meta');
    batch.set(metaDocRef, {
      total_count: totalCount,
      unseen_count: unseenCount,
    });

    // Execute batch
    await batch.commit();

    console.log('Successfully migrated notifications for user:', userId, {
      migrated: totalCount,
      total_count: totalCount,
      unseen_count: unseenCount,
    });

    return {
      migrated: totalCount,
      total_count: totalCount,
      unseen_count: unseenCount,
      notification_ids: migratedNotifications,
    };
  } catch (error) {
    console.error('Error migrating user notifications:', error);
    throw error;
  }
}

module.exports = {
  createNotification,
  updateNotificationMetadata,
  getNotifications,
  markNotificationAsSeen,
  markAllNotificationsAsSeen,
  getNotificationMetadata,
  migrateUserNotifications,
  formatDateRange,
  NOTIFICATION_TYPES,
};
