const {initializeFirebase} = require('../config/firebase');
const {Timestamp} = require('firebase-admin/firestore');

/**
 * Generic notification types with their content generators
 */
const NOTIFICATION_TYPES = {
  SHOPPING_LIST_READY: 'shopping_list_ready',
  MEAL_PLAN_READY: 'meal_plan_ready',
  MEAL_REPLACEMENT_READY: 'meal_replacement_ready',
  MEAL_IMAGE_READY: 'meal_image_ready',
  WATER_REMINDER: 'water_reminder',
  CUSTOM: 'custom',
};

/**
 * Create a notification for a user
 * @param {string} userId - User ID
 * @param {Object} notificationData - Notification data
 * @param {string} notificationData.type - Notification type from NOTIFICATION_TYPES
 * @param {Object} notificationData.content - Content specific to notification type
 * @param {Object} notificationData.action - Optional action object for navigation
 * @param {string} notificationData.customId - Optional custom ID prefix
 * @return {Object} Created notification data
 */
async function createNotification(userId, notificationData) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Get user profile to determine preferred language
    const userDoc = await db.collection('users').doc(userId).get();
    const userData = userDoc.data();
    const preferredLanguage = userData?.preferred_language || 'ar';

    // Generate notification content based on type and language
    const notificationContent = generateNotificationContent(
      notificationData.type,
      notificationData.content,
      preferredLanguage
    );

    // Generate unique notification ID
    const notificationId = generateNotificationId(notificationData.type, notificationData.customId);

    // Create notification document
    const notification = {
      id: notificationId,
      title: notificationContent.title,
      message: notificationContent.message,
      timestamp: Timestamp.now(),
      seen: false,
      type: notificationData.type,
      ...(notificationData.action && { action: notificationData.action }),
    };

    // Add notification to user's notifications collection
    await db.collection('users').doc(userId).collection('notifications').doc(notificationId).set(notification);

    // Update notification metadata
    await updateNotificationMetadata(userId);

    console.log('Notification created successfully for user:', userId, 'Type:', notificationData.type);
    
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Generate notification content based on type and language
 * @param {string} type - Notification type
 * @param {Object} content - Content data specific to notification type
 * @param {string} preferredLanguage - User's preferred language
 * @return {Object} Generated notification content with title and message
 */
function generateNotificationContent(type, content, preferredLanguage) {
  const isArabic = preferredLanguage === 'ar';

  switch (type) {
    case NOTIFICATION_TYPES.SHOPPING_LIST_READY:
      return generateShoppingListContent(content, isArabic);

    case NOTIFICATION_TYPES.MEAL_PLAN_READY:
      return generateMealPlanContent(content, isArabic);

    case NOTIFICATION_TYPES.MEAL_REPLACEMENT_READY:
      return generateMealReplacementContent(content, isArabic);

    case NOTIFICATION_TYPES.MEAL_IMAGE_READY:
      return generateMealImageContent(content, isArabic);

    case NOTIFICATION_TYPES.WATER_REMINDER:
      return generateWaterReminderContent(content, isArabic);

    case NOTIFICATION_TYPES.CUSTOM:
      return {
        title: content.title || (isArabic ? 'إشعار' : 'Notification'),
        message: content.message || (isArabic ? 'لديك إشعار جديد' : 'You have a new notification'),
      };
    
    default:
      return {
        title: isArabic ? 'إشعار' : 'Notification',
        message: isArabic ? 'لديك إشعار جديد' : 'You have a new notification',
      };
  }
}

/**
 * Generate shopping list notification content
 * @param {Object} content - Shopping list content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateShoppingListContent(content, isArabic) {
  const { title, dateRange } = content;
  
  if (isArabic) {
    return {
      title: 'قائمة التسوق جاهزة',
      message: `قائمة التسوق "${title}" للفترة ${dateRange} جاهزة! اضغط لعرض قائمة التسوق.`,
    };
  } else {
    return {
      title: 'Shopping List Ready',
      message: `Your shopping list "${title}" for ${dateRange} is ready! Tap to view your shopping list.`,
    };
  }
}

/**
 * Generate meal plan notification content
 * @param {Object} content - Meal plan content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateMealPlanContent(content, isArabic) {
  const { duration, planType } = content;

  if (isArabic) {
    return {
      title: 'خطة الوجبات جاهزة',
      message: `خطة الوجبات الخاصة بك لمدة ${duration} أيام جاهزة! اضغط لعرض خطة الوجبات.`,
    };
  } else {
    return {
      title: 'Meal Plan Ready',
      message: `Your ${duration}-day meal plan is ready! Tap to view your meal plan.`,
    };
  }
}

/**
 * Generate meal replacement notification content
 * @param {Object} content - Meal replacement content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateMealReplacementContent(content, isArabic) {
  const { mealName } = content;

  if (isArabic) {
    return {
      title: 'بديل الوجبة جاهز',
      message: `بديل الوجبة الجديد "${mealName}" جاهز! اضغط لعرض خيارات الوجبة.`,
    };
  } else {
    return {
      title: 'Meal Replacement Ready',
      message: `Your new meal replacement "${mealName}" is ready! Tap to view your meal options.`,
    };
  }
}

/**
 * Generate meal image notification content
 * @param {Object} content - Meal image content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateMealImageContent(content, isArabic) {
  const { mealName } = content;

  if (isArabic) {
    return {
      title: 'صورة الوجبة جاهزة',
      message: `تم إنشاء صورة للوجبة "${mealName}" بنجاح! اضغط لعرض الصورة.`,
    };
  } else {
    return {
      title: 'Meal Image Ready',
      message: `Image for "${mealName}" has been generated successfully! Tap to view the image.`,
    };
  }
}

/**
 * Generate water reminder notification content
 * @param {Object} content - Water reminder content data
 * @param {boolean} isArabic - Whether to use Arabic language
 * @return {Object} Notification content
 */
function generateWaterReminderContent(content, isArabic) {
  const { targetAmount, currentAmount } = content;
  
  if (isArabic) {
    return {
      title: 'تذكير شرب الماء',
      message: `حان وقت شرب الماء! لقد شربت ${currentAmount || 0} من ${targetAmount} أكواب اليوم.`,
    };
  } else {
    return {
      title: 'Water Reminder',
      message: `Time to drink water! You've had ${currentAmount || 0} of ${targetAmount} glasses today.`,
    };
  }
}

/**
 * Generate unique notification ID
 * @param {string} type - Notification type
 * @param {string} customId - Optional custom ID prefix
 * @return {string} Generated notification ID
 */
function generateNotificationId(type, customId) {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substr(2, 9);
  const prefix = customId || type;
  return `${prefix}_${timestamp}_${randomSuffix}`;
}

/**
 * Update notification metadata for the user
 * @param {string} userId - User ID
 */
async function updateNotificationMetadata(userId) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Get current metadata
    const userDoc = await db.collection('users').doc(userId).get();
    const userData = userDoc.data();
    const currentMeta = userData?.notification_meta || { total_count: 0, unseen_count: 0 };

    // Increment counts
    const updatedMeta = {
      total_count: (currentMeta.total_count || 0) + 1,
      unseen_count: (currentMeta.unseen_count || 0) + 1,
    };

    // Update user document
    await db.collection('users').doc(userId).update({
      notification_meta: updatedMeta,
    });

    console.log('Notification metadata updated for user:', userId, updatedMeta);
  } catch (error) {
    console.error('Error updating notification metadata:', error);
    throw error;
  }
}

/**
 * Format date range for notification messages
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {string} preferredLanguage - User's preferred language
 * @return {string} Formatted date range
 */
function formatDateRange(startDate, endDate, preferredLanguage) {
  // Validate input parameters
  if (!startDate || !(startDate instanceof Date)) {
    console.error('formatDateRange: Invalid startDate provided:', startDate);
    return 'Invalid date';
  }

  const options = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  };

  // If endDate is null or invalid, format only the start date
  if (!endDate || !(endDate instanceof Date)) {
    if (preferredLanguage === 'en') {
      return startDate.toLocaleDateString('en-US', options);
    } else {
      return startDate.toLocaleDateString('ar-SA', options);
    }
  }

  // Format date range with both start and end dates
  if (preferredLanguage === 'en') {
    const start = startDate.toLocaleDateString('en-US', options);
    const end = endDate.toLocaleDateString('en-US', options);
    return startDate.getTime() === endDate.getTime() ? start : `${start} - ${end}`;
  } else {
    // Arabic formatting
    const start = startDate.toLocaleDateString('ar-SA', options);
    const end = endDate.toLocaleDateString('ar-SA', options);
    return startDate.getTime() === endDate.getTime() ? start : `${start} - ${end}`;
  }
}

module.exports = {
  createNotification,
  updateNotificationMetadata,
  formatDateRange,
  NOTIFICATION_TYPES,
};
