# Endpoint Organization Guide

This guide explains how endpoints are organized in the ultra-modular architecture.

## 📁 Current Endpoint Structure

### User Domain (`routes/user/`)
```
user/
├── index.js           # Router aggregator
├── getProfile.js      # GET /user/profile
├── updateProfile.js   # PUT /user/profile
└── getUserProfile.js  # GET /user/:userId/profile
```

### Meal Plan Domain (`routes/mealPlan/`)
```
mealPlan/
├── index.js              # Router aggregator
├── generatePublic.js     # POST /generate-meal-plan
├── generateAuth.js       # POST /meal-plans/generate
└── getUserMealPlans.js   # GET /meal-plans/:userId
```

## 🔧 Endpoint File Template

Each endpoint file follows this structure:

```javascript
const {authenticateUser} = require('../../middleware/auth');
const {validationSchema} = require('../../utils/validators');
const {serviceFunction} = require('../../services/domainService');

/**
 * HTTP_METHOD /endpoint/path
 * Description of what this endpoint does
 * Authentication requirements
 */
async function endpointHandler(req, res) {
  try {
    // 1. Validate request
    const {error, value} = validationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    // 2. Extract data
    const {param1, param2} = value;
    const userId = req.body.userId; // From auth middleware if needed

    // 3. Call service
    const result = await serviceFunction(param1, param2);

    // 4. Return response
    res.json({
      success: true,
      data: result,
      message: 'Operation completed successfully',
    });
  } catch (error) {
    console.error('Error in endpointHandler:', error);
    res.status(500).json({
      error: 'Failed to complete operation',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'get|post|put|delete',
  path: '/endpoint/path',
  middleware: [authenticateUser], // Optional
  handler: endpointHandler,
};
```

## ➕ Adding New Endpoints

### Step 1: Create Endpoint File
```bash
# Example: Adding GET /user/preferences
touch functions/routes/user/getPreferences.js
```

### Step 2: Implement Endpoint
```javascript
// functions/routes/user/getPreferences.js
const {authenticateUser} = require('../../middleware/auth');
const {getUserPreferences} = require('../../services/userService');

async function getPreferences(req, res) {
  try {
    const userId = req.body.userId;
    const preferences = await getUserPreferences(userId);
    
    res.json({
      success: true,
      preferences,
    });
  } catch (error) {
    console.error('Error getting preferences:', error);
    res.status(500).json({
      error: 'Failed to get preferences',
      details: error.message,
    });
  }
}

module.exports = {
  method: 'get',
  path: '/user/preferences',
  middleware: [authenticateUser],
  handler: getPreferences,
};
```

### Step 3: Register in Domain Router
```javascript
// functions/routes/user/index.js
const getPreferences = require('./getPreferences'); // Add this line

const endpoints = [
  getProfile,
  updateProfile,
  getUserProfile,
  getPreferences, // Add this line
];
```

## 🆕 Adding New Domains

### Step 1: Create Domain Directory
```bash
mkdir functions/routes/newDomain
```

### Step 2: Create Endpoint Files
```bash
touch functions/routes/newDomain/endpoint1.js
touch functions/routes/newDomain/endpoint2.js
```

### Step 3: Create Domain Router
```javascript
// functions/routes/newDomain/index.js
const express = require('express');

const endpoint1 = require('./endpoint1');
const endpoint2 = require('./endpoint2');

const router = express.Router();

const endpoints = [
  endpoint1,
  endpoint2,
];

endpoints.forEach((endpoint) => {
  const {method, path, middleware = [], handler} = endpoint;
  router[method](path, ...middleware, handler);
});

module.exports = router;
```

### Step 4: Register Domain in Main Router
```javascript
// functions/routes/index.js
const newDomainRoutes = require('./newDomain'); // Add this line

router.use('/', newDomainRoutes); // Add this line
```

## 🎯 Benefits

### For Developers
- **Easy Navigation**: Find any endpoint instantly
- **Clear Responsibility**: Each file has one job
- **Consistent Pattern**: All endpoints follow same structure
- **Easy Testing**: Test endpoints in isolation
- **Simple Debugging**: Errors are localized to specific files

### For Maintenance
- **Minimal Impact Changes**: Modifying one endpoint doesn't affect others
- **Easy Code Reviews**: Small, focused files
- **Clear Dependencies**: Explicit imports
- **Scalable Structure**: Easy to add new endpoints/domains

### For Teams
- **Parallel Development**: Multiple developers can work on different endpoints
- **Clear Ownership**: Each endpoint can have a clear owner
- **Easy Onboarding**: New developers can understand structure quickly
- **Consistent Standards**: All endpoints follow same patterns

## 📊 File Size Comparison

| Structure | Files | Avg Lines/File | Max Lines/File |
|-----------|-------|----------------|----------------|
| Before (Monolithic) | 1 | 420 | 420 |
| After (Ultra-Modular) | 20+ | ~35 | ~50 |

This ultra-modular approach ensures that no single file becomes too large or complex, making the codebase highly maintainable and developer-friendly.
