const { expect } = require('chai');

/**
 * Test suite to verify Firebase Functions handle the new snake_case data structure
 */
describe('Snake Case Data Structure Tests', () => {
  describe('User Profile Data Extraction', () => {
    it('should extract preferences from plan_preferences with snake_case field names', () => {
      // Mock user profile with new snake_case structure
      const userProfile = {
        id: 'user_123',
        email: '<EMAIL>',
        display_name: 'Test User',
        preferred_language: 'ar',
        plan_preferences: {
          // Health Information
          allergies: ['peanuts'],
          dietary_restrictions: ['vegetarian'],
          health_conditions: ['none'],
          medications: ['vitamin_d'],
          
          // Goals
          health_goal: 'maintain',
          target_weight: 70.0,
          daily_calorie_goal: 2000,
          selected_calorie_recommendation: 'moderate',
          protein_goal: 150.0,
          carbs_goal: 250.0,
          fat_goal: 67.0,
          fiber_goal: 25.0,
          
          // Diet Type & Macros
          selected_diet_type: 'balanced',
          custom_macro_distribution: {
            carbs_percentage: 50.0,
            protein_percentage: 20.0,
            fat_percentage: 30.0
          },
          use_custom_macros: false,
          
          // Food Preferences
          favorite_ingredients: ['chicken', 'rice'],
          disliked_ingredients: ['fish'],
          favorite_cuisines: ['italian', 'mediterranean'],
          meals_per_day: 3,
          snacks_per_day: 2,
          
          // Activity & Physical
          activity_level: 'moderate',
          daily_burned_calories: 2000,
          use_manual_calories: true,
          unit_system: 'metric',
          height: 175.0,
          weight: 70.0,
          date_of_birth: '1990-01-01',
          gender: 'male'
        },
        notification_settings: {
          meal_reminders: true,
          water_reminders: true,
          workout_reminders: false,
          progress_updates: true
        }
      };

      // Simulate the preference extraction logic from generateAuth.js
      const planPrefs = userProfile.plan_preferences || {};
      
      const preferences = {
        // Basic dietary information (from plan_preferences with snake_case)
        dietaryRestrictions: planPrefs.dietary_restrictions || userProfile.dietary_restrictions || [],
        allergies: planPrefs.allergies || userProfile.allergies || [],
        cuisinePreferences: planPrefs.favorite_cuisines || userProfile.favorite_cuisines || [],
        favoriteIngredients: planPrefs.favorite_ingredients || userProfile.favorite_ingredients || [],
        dislikedIngredients: planPrefs.disliked_ingredients || userProfile.disliked_ingredients || [],

        // Meal structure (from plan_preferences with snake_case)
        mealsPerDay: planPrefs.meals_per_day || userProfile.meals_per_day || 3,
        snacksPerDay: planPrefs.snacks_per_day || userProfile.snacks_per_day || 2,

        // Nutritional goals (from plan_preferences with snake_case)
        calorieGoal: planPrefs.daily_calorie_goal || userProfile.daily_calorie_goal || 2000,
        proteinGoal: Math.round(planPrefs.protein_goal || userProfile.protein_goal || 0),
        carbsGoal: Math.round(planPrefs.carbs_goal || userProfile.carbs_goal || 0),
        fatGoal: Math.round(planPrefs.fat_goal || userProfile.fat_goal || 0),
        fiberGoal: Math.round(planPrefs.fiber_goal || userProfile.fiber_goal || 0),

        // Physical information for better meal planning (from plan_preferences with snake_case)
        height: planPrefs.height || userProfile.height,
        weight: planPrefs.weight || userProfile.weight,
        age: (planPrefs.date_of_birth || userProfile.date_of_birth) ?
          Math.floor((Date.now() - new Date(planPrefs.date_of_birth || userProfile.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : null,
        gender: planPrefs.gender || userProfile.gender,
        activityLevel: planPrefs.activity_level || userProfile.activity_level,
        useManualCalories: planPrefs.use_manual_calories || userProfile.use_manual_calories,
        dailyBurnedCalories: planPrefs.daily_burned_calories || userProfile.daily_burned_calories,

        // Health goals (from plan_preferences with snake_case)
        healthGoal: planPrefs.health_goal || userProfile.health_goal,
        targetWeight: planPrefs.target_weight || userProfile.target_weight,

        // Health conditions (from plan_preferences with snake_case)
        healthConditions: planPrefs.health_conditions || userProfile.health_conditions || [],
        medications: planPrefs.medications || userProfile.medications || [],

        // System preferences (from plan_preferences with snake_case)
        unitSystem: planPrefs.unit_system || userProfile.unit_system || 'metric',
        preferredLanguage: userProfile.preferred_language || 'ar',

        dietType: 'normal', // Default diet type, can be enhanced later
      };

      // Verify that preferences are correctly extracted from snake_case fields
      expect(preferences.dietaryRestrictions).to.deep.equal(['vegetarian']);
      expect(preferences.allergies).to.deep.equal(['peanuts']);
      expect(preferences.cuisinePreferences).to.deep.equal(['italian', 'mediterranean']);
      expect(preferences.favoriteIngredients).to.deep.equal(['chicken', 'rice']);
      expect(preferences.dislikedIngredients).to.deep.equal(['fish']);
      expect(preferences.mealsPerDay).to.equal(3);
      expect(preferences.snacksPerDay).to.equal(2);
      expect(preferences.calorieGoal).to.equal(2000);
      expect(preferences.proteinGoal).to.equal(150);
      expect(preferences.carbsGoal).to.equal(250);
      expect(preferences.fatGoal).to.equal(67);
      expect(preferences.fiberGoal).to.equal(25);
      expect(preferences.height).to.equal(175.0);
      expect(preferences.weight).to.equal(70.0);
      expect(preferences.gender).to.equal('male');
      expect(preferences.activityLevel).to.equal('moderate');
      expect(preferences.useManualCalories).to.equal(true);
      expect(preferences.dailyBurnedCalories).to.equal(2000);
      expect(preferences.healthGoal).to.equal('maintain');
      expect(preferences.targetWeight).to.equal(70.0);
      expect(preferences.healthConditions).to.deep.equal(['none']);
      expect(preferences.medications).to.deep.equal(['vitamin_d']);
      expect(preferences.unitSystem).to.equal('metric');
      expect(preferences.preferredLanguage).to.equal('ar');
      expect(preferences.age).to.be.a('number');
      expect(preferences.age).to.be.greaterThan(30); // Should be around 34 years old
    });

    it('should handle backward compatibility with old camelCase structure', () => {
      // Mock user profile with old camelCase structure (fallback)
      const userProfile = {
        id: 'user_456',
        email: '<EMAIL>',
        displayName: 'Test User 2',
        preferredLanguage: 'en',
        // Old structure - fields at root level with camelCase
        dietaryRestrictions: ['vegan'],
        allergies: ['nuts'],
        favoriteCuisines: ['asian'],
        favoriteIngredients: ['tofu'],
        dislikedIngredients: ['meat'],
        mealsPerDay: 4,
        snacksPerDay: 1,
        dailyCalorieGoal: 1800,
        proteinGoal: 120.0,
        carbsGoal: 200.0,
        fatGoal: 60.0,
        fiberGoal: 30.0,
        height: 165.0,
        weight: 60.0,
        dateOfBirth: '1985-06-15',
        gender: 'female',
        activityLevel: 'active',
        useManualCalories: false,
        dailyBurnedCalories: 2200,
        healthGoal: 'lose_weight',
        targetWeight: 55.0,
        healthConditions: ['diabetes'],
        medications: ['metformin'],
        unitSystem: 'imperial'
      };

      // Simulate the preference extraction logic from generateAuth.js
      const planPrefs = userProfile.plan_preferences || {};
      
      const preferences = {
        // Basic dietary information (fallback to old camelCase)
        dietaryRestrictions: planPrefs.dietary_restrictions || userProfile.dietaryRestrictions || [],
        allergies: planPrefs.allergies || userProfile.allergies || [],
        cuisinePreferences: planPrefs.favorite_cuisines || userProfile.favoriteCuisines || [],
        favoriteIngredients: planPrefs.favorite_ingredients || userProfile.favoriteIngredients || [],
        dislikedIngredients: planPrefs.disliked_ingredients || userProfile.dislikedIngredients || [],

        // Meal structure (fallback to old camelCase)
        mealsPerDay: planPrefs.meals_per_day || userProfile.mealsPerDay || 3,
        snacksPerDay: planPrefs.snacks_per_day || userProfile.snacksPerDay || 2,

        // Nutritional goals (fallback to old camelCase)
        calorieGoal: planPrefs.daily_calorie_goal || userProfile.dailyCalorieGoal || 2000,
        proteinGoal: Math.round(planPrefs.protein_goal || userProfile.proteinGoal || 0),
        carbsGoal: Math.round(planPrefs.carbs_goal || userProfile.carbsGoal || 0),
        fatGoal: Math.round(planPrefs.fat_goal || userProfile.fatGoal || 0),
        fiberGoal: Math.round(planPrefs.fiber_goal || userProfile.fiberGoal || 0),

        // Physical information (fallback to old camelCase)
        height: planPrefs.height || userProfile.height,
        weight: planPrefs.weight || userProfile.weight,
        age: (planPrefs.date_of_birth || userProfile.dateOfBirth) ?
          Math.floor((Date.now() - new Date(planPrefs.date_of_birth || userProfile.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : null,
        gender: planPrefs.gender || userProfile.gender,
        activityLevel: planPrefs.activity_level || userProfile.activityLevel,
        useManualCalories: planPrefs.use_manual_calories || userProfile.useManualCalories,
        dailyBurnedCalories: planPrefs.daily_burned_calories || userProfile.dailyBurnedCalories,

        // Health goals (fallback to old camelCase)
        healthGoal: planPrefs.health_goal || userProfile.healthGoal,
        targetWeight: planPrefs.target_weight || userProfile.targetWeight,

        // Health conditions (fallback to old camelCase)
        healthConditions: planPrefs.health_conditions || userProfile.healthConditions || [],
        medications: planPrefs.medications || userProfile.medications || [],

        // System preferences (fallback to old camelCase)
        unitSystem: planPrefs.unit_system || userProfile.unitSystem || 'metric',
        preferredLanguage: userProfile.preferredLanguage || 'ar',

        dietType: 'normal',
      };

      // Verify that preferences are correctly extracted from old camelCase fields
      expect(preferences.dietaryRestrictions).to.deep.equal(['vegan']);
      expect(preferences.allergies).to.deep.equal(['nuts']);
      expect(preferences.cuisinePreferences).to.deep.equal(['asian']);
      expect(preferences.favoriteIngredients).to.deep.equal(['tofu']);
      expect(preferences.dislikedIngredients).to.deep.equal(['meat']);
      expect(preferences.mealsPerDay).to.equal(4);
      expect(preferences.snacksPerDay).to.equal(1);
      expect(preferences.calorieGoal).to.equal(1800);
      expect(preferences.proteinGoal).to.equal(120);
      expect(preferences.carbsGoal).to.equal(200);
      expect(preferences.fatGoal).to.equal(60);
      expect(preferences.fiberGoal).to.equal(30);
      expect(preferences.height).to.equal(165.0);
      expect(preferences.weight).to.equal(60.0);
      expect(preferences.gender).to.equal('female');
      expect(preferences.activityLevel).to.equal('active');
      expect(preferences.useManualCalories).to.equal(false);
      expect(preferences.dailyBurnedCalories).to.equal(2200);
      expect(preferences.healthGoal).to.equal('lose_weight');
      expect(preferences.targetWeight).to.equal(55.0);
      expect(preferences.healthConditions).to.deep.equal(['diabetes']);
      expect(preferences.medications).to.deep.equal(['metformin']);
      expect(preferences.unitSystem).to.equal('imperial');
      expect(preferences.preferredLanguage).to.equal('en');
      expect(preferences.age).to.be.a('number');
      expect(preferences.age).to.be.greaterThan(35); // Should be around 39 years old
    });
  });

  describe('Firestore Data Structure', () => {
    it('should use snake_case field names for saving meal plan data', () => {
      // Mock day data with snake_case structure
      const dayData = {
        date: new Date(),
        meals: [
          {
            id: 'meal_123',
            name: 'Breakfast',
            type: 'breakfast',
            ingredients: [
              {
                name: 'Oats 50g',
                needs_preparation: true,
                instructions: ['Boil water', 'Add oats', 'Cook for 5 minutes']
              },
              {
                name: 'Banana 1 medium',
                needs_preparation: false,
                instructions: []
              }
            ],
            nutrition: {
              calories: 300,
              protein: 10.0,
              carbs: 50.0,
              fat: 5.0,
              fiber: 8.0
            },
            preparation_time: 10,
            difficulty: 'easy'
          }
        ],
        total_nutrition: {
          calories: 300,
          protein: 10.0,
          carbs: 50.0,
          fat: 5.0,
          fiber: 8.0
        },
        created_at: new Date(),
        last_modified: new Date()
      };

      // Verify snake_case field names are used
      expect(dayData).to.have.property('total_nutrition');
      expect(dayData).to.have.property('created_at');
      expect(dayData).to.have.property('last_modified');
      expect(dayData.meals[0]).to.have.property('preparation_time');
      expect(dayData.meals[0].ingredients[0]).to.have.property('needs_preparation');
      expect(dayData.meals[0].ingredients[0].needs_preparation).to.equal(true);
      expect(dayData.meals[0].ingredients[1].needs_preparation).to.equal(false);
    });
  });
});
