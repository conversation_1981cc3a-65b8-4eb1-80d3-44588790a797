const {authenticateUser} = require('../../middleware/auth');
const {withResponse<PERSON>andler} = require('../../middleware/responseHandler');
const {getNotificationMetadata} = require('../../services/notificationService');

/**
 * GET /notifications/metadata
 * Get notification metadata (total_count, unseen_count) for the authenticated user
 */
async function getMetadataHandler(req, res, validatedData) {
  const authenticatedUserId = req.user.uid; // From auth middleware

  try {
    const metadata = await getNotificationMetadata(authenticatedUserId);

    res.json({
      success: true,
      metadata: metadata,
    });
  } catch (error) {
    console.error('Error getting notification metadata:', error);
    res.status(500).json({
      error: 'Failed to get notification metadata',
      details: error.message,
    });
  }
}

module.exports = {
  method: 'get',
  path: '/notifications/metadata',
  middleware: [authenticateUser],
  handler: getMetadata<PERSON><PERSON><PERSON>,
};
