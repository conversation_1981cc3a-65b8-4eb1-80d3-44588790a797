const {authenticateUser} = require('../../middleware/auth');
const {withResponseHandler} = require('../../middleware/responseHandler');
const {migrateUserNotifications} = require('../../services/notificationService');

/**
 * POST /notifications/migrate
 * Migrate user's notifications from old structure to new structure
 * This endpoint allows users to migrate their own notifications
 */
async function migrateNotificationsHandler(req, res, validatedData) {
  const authenticatedUserId = req.user.uid; // From auth middleware

  try {
    const result = await migrateUserNotifications(authenticatedUserId);

    res.json({
      success: true,
      message: 'Notifications migrated successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error migrating notifications:', error);
    res.status(500).json({
      error: 'Failed to migrate notifications',
      details: error.message,
    });
  }
}

module.exports = {
  method: 'post',
  path: '/notifications/migrate',
  middleware: [authenticateUser],
  handler: migrateNotifications<PERSON>and<PERSON>,
};
