const {authenticateUser} = require('../../middleware/auth');
const {withResponseHandler} = require('../../middleware/responseHandler');
const {markNotificationAsSeen} = require('../../services/notificationService');

/**
 * PATCH /notifications/:notificationId/seen
 * Mark a specific notification as seen for the authenticated user
 */
async function markAsSeenHandler(req, res, validatedData) {
  const authenticatedUserId = req.user.uid; // From auth middleware
  const {notificationId} = req.params;

  if (!notificationId) {
    return res.status(400).json({
      error: 'Notification ID is required',
    });
  }

  try {
    await markNotificationAsSeen(authenticatedUserId, notificationId);

    res.json({
      success: true,
      message: 'Notification marked as seen',
    });
  } catch (error) {
    console.error('Error marking notification as seen:', error);
    
    if (error.message === 'Notification not found') {
      return res.status(404).json({
        error: 'Notification not found',
      });
    }

    res.status(500).json({
      error: 'Failed to mark notification as seen',
      details: error.message,
    });
  }
}

module.exports = {
  method: 'patch',
  path: '/notifications/:notificationId/seen',
  middleware: [authenticateUser],
  handler: markAsSeenHandler,
};
