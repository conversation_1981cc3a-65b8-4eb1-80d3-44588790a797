const express = require('express');

// Import notification route handlers
const getNotifications = require('./getNotifications');
const markAsSeen = require('./markAsSeen');
const markAllAsSeen = require('./markAllAsSeen');
const getMetadata = require('./getMetadata');
const migrate = require('./migrate');

const router = express.Router();

// Register notification routes
const routes = [
  getNotifications,
  markAsSeen,
  markAllAsSeen,
  getMetadata,
  migrate,
];

routes.forEach((route) => {
  const {method, path, middleware = [], handler} = route;

  // Apply middleware and handler based on HTTP method
  switch (method.toLowerCase()) {
  case 'get':
    router.get(path, ...middleware, handler);
    break;
  case 'post':
    router.post(path, ...middleware, handler);
    break;
  case 'put':
    router.put(path, ...middleware, handler);
    break;
  case 'delete':
    router.delete(path, ...middleware, handler);
    break;
  case 'patch':
    router.patch(path, ...middleware, handler);
    break;
  default:
    console.warn(`Unsupported HTTP method: ${method} for route: ${path}`);
  }
});

module.exports = router;
