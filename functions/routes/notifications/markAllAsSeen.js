const {authenticateUser} = require('../../middleware/auth');
const {withResponse<PERSON>and<PERSON>} = require('../../middleware/responseHandler');
const {markAllNotificationsAsSeen} = require('../../services/notificationService');

/**
 * PATCH /notifications/mark-all-seen
 * Mark all notifications as seen for the authenticated user
 */
async function markAllAsSeenHandler(req, res, validatedData) {
  const authenticatedUserId = req.user.uid; // From auth middleware

  try {
    const count = await markAllNotificationsAsSeen(authenticatedUserId);

    res.json({
      success: true,
      message: 'All notifications marked as seen',
      count: count,
    });
  } catch (error) {
    console.error('Error marking all notifications as seen:', error);
    res.status(500).json({
      error: 'Failed to mark all notifications as seen',
      details: error.message,
    });
  }
}

module.exports = {
  method: 'patch',
  path: '/notifications/mark-all-seen',
  middleware: [authenticateUser],
  handler: mark<PERSON>ll<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
};
