const {authenticateUser} = require('../../middleware/auth');
const {withResponseHandler} = require('../../middleware/responseHandler');
const {getNotifications} = require('../../services/notificationService');

/**
 * GET /notifications
 * Get notifications for the authenticated user with pagination
 * Query parameters:
 * - limit: Number of notifications to fetch (default: 20, max: 50)
 * - lastNotificationId: Last notification ID for pagination (optional)
 */
async function getNotificationsHandler(req, res, validatedData) {
  const authenticatedUserId = req.user.uid; // From auth middleware
  const {limit = 20, lastNotificationId} = req.query;

  // Validate limit
  const parsedLimit = Math.min(parseInt(limit) || 20, 50);

  try {
    const result = await getNotifications(authenticatedUserId, parsedLimit, lastNotificationId);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error getting notifications:', error);
    res.status(500).json({
      error: 'Failed to get notifications',
      details: error.message,
    });
  }
}

module.exports = {
  method: 'get',
  path: '/notifications',
  middleware: [authenticateUser],
  handler: getNotificationsHandler,
};
