const {authenticateUser} = require('../../middleware/auth');
const {getUserProfile} = require('../../services/userService');

/**
 * GET /user/:userId/profile
 * Gets a specific user's public profile (admin only or public fields)
 * Requires authentication
 */
async function getUserProfileById(req, res) {
  try {
    const {userId} = req.params;
    const requestingUserId = req.body.userId; // From auth middleware

    // Only allow users to access their own profile for now
    // In the future, you might want to add admin checks or public profile fields
    if (requestingUserId !== userId) {
      return res.status(403).json({
        error: 'Forbidden: Cannot access other user\'s profile',
      });
    }

    const userProfile = await getUserProfile(userId);

    if (!userProfile) {
      return res.status(404).json({
        error: 'User profile not found',
      });
    }

    res.json({
      success: true,
      profile: userProfile,
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({
      error: 'Failed to fetch user profile',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'get',
  path: '/user/:userId/profile',
  middleware: [authenticateUser],
  handler: getUserProfileById,
};
