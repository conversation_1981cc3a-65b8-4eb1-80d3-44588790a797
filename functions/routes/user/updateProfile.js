const {authenticateUser} = require('../../middleware/auth');
const {updateUserProfile} = require('../../services/userService');

/**
 * PUT /user/profile
 * Updates the authenticated user's profile
 * Requires authentication
 */
async function updateProfile(req, res) {
  try {
    const userId = req.body.userId; // From auth middleware

    // Extract update fields (exclude userId and sensitive fields)
    const {
      displayName,
      photoURL,
      onboardingCompleted,
      preferences,
      ...otherUpdates
    } = req.body;

    const allowedUpdates = {
      displayName,
      photoURL,
      onboardingCompleted,
      preferences,
      ...otherUpdates,
    };

    // Remove undefined values
    Object.keys(allowedUpdates).forEach((key) => {
      if (allowedUpdates[key] === undefined) {
        delete allowedUpdates[key];
      }
    });

    if (Object.keys(allowedUpdates).length === 0) {
      return res.status(400).json({
        error: 'No valid fields to update',
      });
    }

    await updateUserProfile(userId, allowedUpdates);

    res.json({
      success: true,
      message: 'Profile updated successfully',
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({
      error: 'Failed to update user profile',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'put',
  path: '/user/profile',
  middleware: [authenticateUser],
  handler: updateProfile,
};
