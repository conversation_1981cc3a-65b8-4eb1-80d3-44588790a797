const express = require('express');

// Import endpoint configurations
const getProfile = require('./getProfile');
const updateProfile = require('./updateProfile');
const getUserProfile = require('./getUserProfile');

const router = express.Router();

// Register routes
const endpoints = [
  getProfile,
  updateProfile,
  getUserProfile,
];

endpoints.forEach((endpoint) => {
  const {method, path, middleware = [], handler} = endpoint;
  router[method](path, ...middleware, handler);
});

module.exports = router;
