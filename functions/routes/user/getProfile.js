const {authenticateUser} = require('../../middleware/auth');
const {getUserProfile} = require('../../services/userService');

/**
 * GET /user/profile
 * Gets the authenticated user's profile
 * Requires authentication
 */
async function getProfile(req, res) {
  try {
    const userId = req.body.userId; // From auth middleware

    const userProfile = await getUserProfile(userId);

    if (!userProfile) {
      return res.status(404).json({
        error: 'User profile not found',
      });
    }

    res.json({
      success: true,
      profile: userProfile,
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({
      error: 'Failed to fetch user profile',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'get',
  path: '/user/profile',
  middleware: [authenticateUser],
  handler: getProfile,
};
