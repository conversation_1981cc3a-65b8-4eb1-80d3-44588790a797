const express = require('express');

// Import domain routers
const userRoutes = require('./user');
const mealPlanRoutes = require('./mealPlan');
const shoppingListRoutes = require('./shoppingList');
const notificationRoutes = require('./notifications');

const router = express.Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'easydietai-functions',
  });
});

// Register domain routes
router.use('/', userRoutes);
router.use('/', mealPlanRoutes);
router.use('/', shoppingListRoutes);
router.use('/', notificationRoutes);

// 404 handler for API routes
router.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
  });
});

module.exports = router;
