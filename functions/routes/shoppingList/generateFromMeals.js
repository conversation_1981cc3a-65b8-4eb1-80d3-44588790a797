const {authenticateUser} = require('../../middleware/auth');
const {withResponseHandler, createSuccessResponse} = require('../../middleware/responseHandler');
const {shoppingListGenerationSchema} = require('../../utils/validators');
const {generateShoppingListFromMeals, validateMealsExistInDateRange} = require('../../services/shoppingListService');

/**
 * POST /shopping-list/generate-from-meals
 * Generate shopping list from meals in a date range
 * Requires authentication and date range parameters
 * Returns immediately with success status, processes generation in background
 */
async function generateShoppingListFromMealsHandler(req, res, validatedData) {
  const authenticatedUserId = req.user.uid; // From auth middleware
  const {start_date, end_date, shopping_id} = validatedData; // Already validated by middleware

  const startDate = new Date(start_date);
  const endDate = new Date(end_date);

  console.log('Validating meals exist for shopping list generation for user:', authenticatedUserId);
  console.log('Date range:', start_date, 'to', end_date);
  console.log('Shopping ID for replacement:', shopping_id || 'None (new list)');

  // Validate that meals exist in the date range BEFORE responding to client
  // This will throw an error if no meals found or validation fails
  const validationResult = await validateMealsExistInDateRange(authenticatedUserId, startDate, endDate);

  console.log('Validation successful:', validationResult.message);
  console.log('Starting background shopping list generation for user:', authenticatedUserId);

  // Start background processing with optional replacement ID (don't await)
  processBackgroundShoppingListGeneration(authenticatedUserId, startDate, endDate, shopping_id);

  // Return success response (handled by response middleware)
  const responseMessage = shopping_id ?
    'تم بدء استبدال قائمة التسوق في الخلفية' :
    'تم بدء إنشاء قائمة التسوق في الخلفية';

  return createSuccessResponse(
    {
      meal_count: validationResult.mealCount,
      is_replacement: !!shopping_id,
      ...(shopping_id && {replacing_shopping_id: shopping_id}),
    },
    responseMessage,
  );
}

/**
 * Process shopping list generation in background
 * @param {string} userId - User ID
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {string} shoppingIdToReplace - Optional shopping list ID to replace
 */
async function processBackgroundShoppingListGeneration(userId, startDate, endDate, shoppingIdToReplace = null) {
  try {
    const action = shoppingIdToReplace ? 'Replacing' : 'Generating';
    console.log(`Background processing: ${action} shopping list for user:`, userId);

    // Call the service to generate shopping list (with optional replacement)
    await generateShoppingListFromMeals(userId, startDate, endDate, shoppingIdToReplace);

    console.log(`Background processing: Successfully completed shopping list ${action.toLowerCase()} for user:`, userId);
  } catch (error) {
    console.error('Background processing: Error generating shopping list for user:', userId, error);
    // Background errors are logged but don't affect the client response
    // since the client already received a success response
  }
}


module.exports = {
  method: 'post',
  path: '/shopping-list/generate-from-meals',
  middleware: [authenticateUser],
  handler: withResponseHandler(generateShoppingListFromMealsHandler, shoppingListGenerationSchema),
};
