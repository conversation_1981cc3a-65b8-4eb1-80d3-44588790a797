# EasyDietAI Firebase Functions

This directory contains the Firebase Functions for the EasyDietAI application, providing backend services with OpenAI integration for AI-powered meal planning and nutrition analysis.

## Features

- **AI Meal Planning**: Generate personalized meal plans using OpenAI GPT-4
- **Nutrition Analysis**: Analyze food items and provide detailed nutritional information
- **User Management**: Automatic user document creation and cleanup
- **Authentication**: Secure API endpoints with Firebase Auth
- **Error Handling**: Comprehensive error handling and logging

## Setup

### Prerequisites

- Node.js 18 or higher
- Firebase CLI installed globally: `npm install -g firebase-tools`
- OpenAI API key

### Installation

1. Navigate to the functions directory:
   ```bash
   cd functions
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   firebase functions:config:set openai.api_key="your-openai-api-key"
   ```

### Development

1. Build the TypeScript code:
   ```bash
   npm run build
   ```

2. Start the Firebase emulator:
   ```bash
   npm run serve
   ```

3. The functions will be available at:
   - API: `http://localhost:5001/easydietai-app/us-central1/api`
   - Firebase UI: `http://localhost:4000`

### Deployment

1. Build the project:
   ```bash
   npm run build
   ```

2. Deploy to Firebase:
   ```bash
   npm run deploy
   ```

## API Endpoints

### POST /generate-meal-plan
Generate a personalized meal plan using AI.

**Headers:**
- `Authorization: Bearer <firebase-id-token>`

**Body:**
```json
{
  "preferences": {
    "dietaryRestrictions": ["vegetarian"],
    "allergies": ["nuts"],
    "cuisinePreferences": ["mediterranean"],
    "mealsPerDay": 3,
    "calorieGoal": 2000,
    "proteinGoal": 150,
    "carbsGoal": 200,
    "fatGoal": 65
  },
  "duration": 7
}
```

### POST /analyze-nutrition
Analyze the nutritional content of food items.

**Headers:**
- `Authorization: Bearer <firebase-id-token>`

**Body:**
```json
{
  "foodItems": ["1 cup rice", "100g chicken breast"],
  "portion": "1 serving"
}
```

### GET /meal-plans/:userId
Get user's meal plans.

**Headers:**
- `Authorization: Bearer <firebase-id-token>`

**Query Parameters:**
- `limit`: Number of meal plans to return (default: 10)
- `status`: Filter by status (default: 'active')

## Environment Variables

Set these using Firebase Functions config:

```bash
firebase functions:config:set openai.api_key="your-openai-api-key"
```

## Error Handling

The API returns standardized error responses:

```json
{
  "error": "Error message",
  "details": "Detailed error information"
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication required)
- `500`: Internal Server Error

## Monitoring

- View logs: `npm run logs`
- Monitor performance in Firebase Console
- Set up alerts for error rates and response times

## Security

- All endpoints require Firebase Authentication
- Input validation using Joi schemas
- Rate limiting and CORS protection
- Secure OpenAI API key storage

## Testing

Run the test suite:
```bash
npm test
```

For integration testing with Firebase emulators:
```bash
firebase emulators:start
npm run test:integration
```
