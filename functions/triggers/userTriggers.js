const functions = require('firebase-functions');
const {createUserDocument, deleteUserData} = require('../services/userService');

/**
 * Trigger function when user profile is created
 * Automatically creates a user document in Firestore
 */
const onUserCreated = functions.auth.user().onCreate(async (user) => {
  try {
    await createUserDocument(user);
    console.log('User document created for:', user.uid);
  } catch (error) {
    console.error('Error in onUserCreated trigger:', error);
    // Don't throw error to prevent function retry loops
  }
});

/**
 * Trigger function when user is deleted
 * Automatically cleans up all user data from Firestore
 */
const onUserDeleted = functions.auth.user().onDelete(async (user) => {
  try {
    await deleteUserData(user.uid);
    console.log('User data deleted for:', user.uid);
  } catch (error) {
    console.error('Error in onUserDeleted trigger:', error);
    // Don't throw error to prevent function retry loops
  }
});

module.exports = {
  onUserCreated,
  onUserDeleted,
};
