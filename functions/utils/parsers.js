/**
 * Parses meal plan text response from OpenAI
 * @param {string} mealPlanText - Raw response from OpenAI
 * @param {Object} preferences - User preferences for fallback
 * @param {number} duration - Duration for fallback structure
 * @returns {Object} Parsed meal plan object
 */
function parseMealPlan(mealPlanText, preferences, duration) {
  try {
    // Clean the text to extract JSON
    let cleanedText = mealPlanText.trim();

    // Remove markdown code blocks if present
    if (cleanedText.startsWith('```json')) {
      cleanedText = cleanedText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
    } else if (cleanedText.startsWith('```')) {
      cleanedText = cleanedText.replace(/^```\s*/, '').replace(/\s*```$/, '');
    }

    // Find JSON object boundaries
    const startIndex = cleanedText.indexOf('{');
    const lastIndex = cleanedText.lastIndexOf('}');

    if (startIndex !== -1 && lastIndex !== -1 && lastIndex > startIndex) {
      cleanedText = cleanedText.substring(startIndex, lastIndex + 1);
    }

    // Try to parse as JSON
    const parsed = JSON.parse(cleanedText);

    // Ensure the parsed data has the correct structure for Flutter app
    if (parsed.days && Array.isArray(parsed.days)) {
      // Transform the data to match Flutter app expectations
      const transformedDays = parsed.days.map((day, index) => {
        const baseDate = new Date();
        baseDate.setDate(baseDate.getDate() + index);

        return {
          date: baseDate.toISOString(),
          totalNutrition: {
            calories: parseInt(day.totalNutrition?.calories || preferences.calorieGoal || 2000),
            protein: parseFloat(day.totalNutrition?.protein || 0),
            carbs: parseFloat(day.totalNutrition?.carbs || 0),
            fat: parseFloat(day.totalNutrition?.fat || 0),
            fiber: parseFloat(day.totalNutrition?.fiber || 0),
          },
          meals: (day.meals || []).map(meal => ({
            name: meal.name || meal.type || 'وجبة',
            description: meal.description || `وجبة ${meal.type || 'صحية'}`,
            type: meal.type || 'meal',
            ingredients: (meal.ingredients || []).map(ing =>
              typeof ing === 'string' ? {name: ing} : ing
            ),
            nutrition: {
              calories: parseInt(meal.nutrition?.calories || 0),
              protein: parseFloat(meal.nutrition?.protein || 0),
              carbs: parseFloat(meal.nutrition?.carbs || 0),
              fat: parseFloat(meal.nutrition?.fat || 0),
              fiber: parseFloat(meal.nutrition?.fiber || 0),
            },
            preparationTime: meal.preparationTime || meal.prepTime || 15,
            difficulty: meal.difficulty || 'easy',
          })),
        };
      });

      return {
        days: transformedDays,
        rawResponse: parsed.rawResponse || mealPlanText,
      };
    }

    // If parsed data doesn't have the expected structure, try to create a fallback
    console.warn('Parsed data does not have expected structure, attempting to create fallback', {
      parsedKeys: Object.keys(parsed),
      hasDays: !!parsed.days,
      daysType: typeof parsed.days,
    });

    // Check if it's a single meal object (common for meal replacement)
    if (parsed.name || parsed.meals || parsed.ingredients) {
      console.log('Detected single meal object, wrapping in days structure');
      const baseDate = new Date();

      // If it's a single meal, wrap it in the expected structure
      const meal = {
        name: parsed.name || 'وجبة بديلة',
        description: parsed.description || 'وجبة صحية',
        type: parsed.type || 'meal',
        ingredients: (parsed.ingredients || []).map(ing =>
          typeof ing === 'string' ? {name: ing} : ing
        ),
        nutrition: {
          calories: parseInt(parsed.nutrition?.calories || 0),
          protein: parseFloat(parsed.nutrition?.protein || 0),
          carbs: parseFloat(parsed.nutrition?.carbs || 0),
          fat: parseFloat(parsed.nutrition?.fat || 0),
          fiber: parseFloat(parsed.nutrition?.fiber || 0),
        },
        preparationTime: parsed.preparationTime || parsed.prepTime || 15,
        difficulty: parsed.difficulty || 'easy',
      };

      return {
        days: [{
          date: baseDate.toISOString(),
          totalNutrition: meal.nutrition,
          meals: [meal],
        }],
        rawResponse: mealPlanText,
      };
    }

    // If we can't make sense of the structure, fall through to the catch block
    throw new Error('Parsed data does not match expected structure and cannot be converted');
  } catch (error) {
    // If JSON parsing fails, create a structured response
    console.warn('Failed to parse meal plan as JSON, creating fallback structure');

    const fallbackDays = Array.from({length: duration}, (_, i) => {
      const baseDate = new Date();
      baseDate.setDate(baseDate.getDate() + i);

      return {
        date: baseDate.toISOString(),
        totalNutrition: {
          calories: parseInt(preferences.calorieGoal || 2000),
          protein: 0.0,
          carbs: 0.0,
          fat: 0.0,
          fiber: 0.0,
        },
        meals: [],
      };
    });

    return {
      days: fallbackDays,
      rawResponse: mealPlanText,
    };
  }
}

/**
 * Parses nutrition analysis text response from OpenAI
 * @param {string} nutritionText - Raw response from OpenAI
 * @returns {Object} Parsed nutrition data object
 */
function parseNutritionData(nutritionText) {
  try {
    return JSON.parse(nutritionText);
  } catch (error) {
    console.warn('Failed to parse nutrition data as JSON, creating fallback structure');
    return {
      totalNutrition: {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
      },
      breakdown: [],
      healthScore: 0,
      recommendations: [],
      rawResponse: nutritionText,
    };
  }
}

/**
 * Safely extracts content from OpenAI completion response
 * @param {Object} completion - OpenAI completion response
 * @returns {string|null} Extracted content or null
 */
function extractCompletionContent(completion) {
  return completion.choices[0] && 
         completion.choices[0].message && 
         completion.choices[0].message.content;
}

/**
 * Parse shopping list response from OpenAI
 * @param {string} responseText - Raw response from OpenAI
 * @return {Object} Parsed shopping list data
 */
function parseShoppingListResponse(responseText) {
  try {
    // Extract JSON from the response
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in OpenAI response');
    }

    const rawShoppingListData = JSON.parse(jsonMatch[0]);

    // Filter out empty categories (safety check)
    if (rawShoppingListData.categories && Array.isArray(rawShoppingListData.categories)) {
      rawShoppingListData.categories = rawShoppingListData.categories.filter(category => {
        return category.items && Array.isArray(category.items) && category.items.length > 0;
      });

      console.log('Filtered shopping list categories:', rawShoppingListData.categories.length, 'non-empty categories');
    }

    return rawShoppingListData;
  } catch (error) {
    console.error('Error parsing shopping list response:', error);
    throw error;
  }
}

/**
 * Sanitize data for Firestore by removing undefined values
 * @param {any} data - Data to sanitize
 * @return {any} Sanitized data
 */
function sanitizeDataForFirestore(data) {
  if (data === null || data === undefined) {
    return null;
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitizeDataForFirestore(item));
  }

  if (typeof data === 'object' && data !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      if (value !== undefined) {
        sanitized[key] = sanitizeDataForFirestore(value);
      }
    }
    return sanitized;
  }

  return data;
}

module.exports = {
  parseMealPlan,
  parseNutritionData,
  extractCompletionContent,
  parseShoppingListResponse,
  sanitizeDataForFirestore,
};
