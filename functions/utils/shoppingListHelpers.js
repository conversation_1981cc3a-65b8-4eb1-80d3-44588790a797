/**
 * Map Arabic category names to standard category IDs
 * @param {string} arabicName - Arabic category name
 * @return {string} Category ID
 */
function mapArabicNameToCategoryId(arabicName) {
  const categoryMapping = {
    'خضروات': 'vegetables',
    'فواكه': 'fruits',
    'بروتينات': 'proteins',
    'ألبان': 'dairy',
    'حبوب ونشويات': 'grains',
    'توابل وبهارات': 'spices',
    'زيوت ودهون': 'oils',
    'مشروبات': 'beverages',
    'وجبات خفيفة': 'snacks',
    'مجمدات': 'frozen',
    'معلبات': 'canned',
    'مخبوزات': 'bakery',
    'منتجات منزلية': 'household',
  };

  // Return mapped category ID or fallback to a sanitized version of the Arabic name
  return categoryMapping[arabicName] || arabicName.toLowerCase().replace(/\s+/g, '_');
}

/**
 * Get default icon for a category
 * @param {string} categoryId - Category ID
 * @return {string} Category icon emoji
 */
function getDefaultIconForCategory(categoryId) {
  const categoryIcons = {
    'vegetables': '🥬',
    'fruits': '🍎',
    'proteins': '🥩',
    'dairy': '🥛',
    'grains': '🌾',
    'spices': '🧂',
    'oils': '🫒',
    'beverages': '🥤',
    'snacks': '🍿',
    'frozen': '🧊',
    'canned': '🥫',
    'bakery': '🍞',
    'household': '🧽',
  };

  // Return default icon for category or a generic shopping icon
  return categoryIcons[categoryId] || '🛒';
}

module.exports = {
  mapArabicNameToCategoryId,
  getDefaultIconForCategory,
};
