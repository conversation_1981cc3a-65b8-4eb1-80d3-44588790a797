/**
 * Creates a meal plan generation prompt for OpenAI
 * @param {Object} preferences - User dietary preferences
 * @param {number} duration - Number of days for the meal plan
 * @returns {string} Formatted prompt for OpenAI
 */
function createMealPlanPrompt(preferences, duration) {
  const {
    dietaryRestrictions,
    allergies,
    cuisinePreferences,
    favoriteIngredients,
    dislikedIngredients,
    mealsPerDay,
    snacksPerDay,
    calorieGoal,
    proteinGoal,
    carbsGoal,
    fatGoal,
    fiberGoal,
    height,
    weight,
    age,
    gender,
    activityLevel,
    healthGoal,
    targetWeight,
    healthConditions,
    medications,
    unitSystem,
    preferredLanguage,
  } = preferences;

  // Calculate additional context
  const bmi = height && weight ? (weight / ((height / 100) ** 2)).toFixed(1) : null;
  const isArabic = preferredLanguage === 'ar';

  return `
Generate a ${duration}-day meal plan in ${isArabic ? 'Arabic' : 'English'} with the following comprehensive user profile:

**Personal Information:**
- Age: ${age || 'Not specified'}
- Gender: ${gender || 'Not specified'}
- Height: ${height ? `${height} cm` : 'Not specified'}
- Weight: ${weight ? `${weight} kg` : 'Not specified'}
- BMI: ${bmi || 'Not calculated'}
- Activity Level: ${activityLevel || 'moderate'}

**Health Goals:**
- Primary Goal: ${healthGoal || 'maintain weight'}
- Target Weight: ${targetWeight ? `${targetWeight} kg` : 'Not specified'}
- Health Conditions: ${healthConditions?.length > 0 ? healthConditions.join(', ') : 'None'}
- Medications: ${medications?.length > 0 ? medications.join(', ') : 'None'}

**Nutritional Targets:**
- Daily calorie target: ${calorieGoal} calories
- Protein goal: ${proteinGoal > 0 ? `${proteinGoal}g` : 'Not specified'}
- Carbs goal: ${carbsGoal > 0 ? `${carbsGoal}g` : 'Not specified'}
- Fat goal: ${fatGoal > 0 ? `${fatGoal}g` : 'Not specified'}
- Fiber goal: ${fiberGoal > 0 ? `${fiberGoal}g` : 'Not specified'}

**Meal Structure:**
- Meals per day: ${mealsPerDay}
- Snacks per day: ${snacksPerDay}

**Dietary Preferences:**
- Dietary restrictions: ${dietaryRestrictions?.length > 0 ? dietaryRestrictions.join(', ') : 'None'}
- Allergies: ${allergies?.length > 0 ? allergies.join(', ') : 'None'}
- Preferred cuisines: ${cuisinePreferences?.length > 0 ? cuisinePreferences.join(', ') : 'Any'}
- Favorite ingredients: ${favoriteIngredients?.length > 0 ? favoriteIngredients.join(', ') : 'None specified'}
- Disliked ingredients: ${dislikedIngredients?.length > 0 ? dislikedIngredients.join(', ') : 'None specified'}

**Requirements:**
1. Provide balanced nutrition aligned with the user's health goals and nutritional targets
2. Consider the user's activity level and BMI for appropriate portion sizes
3. Incorporate preferred cuisines and favorite ingredients when possible
4. Avoid disliked ingredients and respect all dietary restrictions and allergies
5. Account for any health conditions and medications in meal planning
6. Include variety in ingredients and cooking methods
7. Ensure meals are practical and achievable
8. Include estimated preparation time for each meal
9. Provide nutritional breakdown for each meal (calories, protein, carbs, fat, fiber)
10. Use ${isArabic ? 'Arabic' : 'English'} language for all meal names and descriptions
11. Consider cultural food preferences if Arabic language is selected
12. **IMPORTANT**: For each ingredient, determine if it needs preparation/cooking:
    - Set needs_preparation: true for ingredients requiring cooking, preparation, or processing
    - Set needs_preparation: false for ready-to-eat items (fruits, nuts, yogurt, etc.)
    - Provide step-by-step instructions array for ingredients that need preparation
    - Use empty instructions array for simple ingredients that don't need preparation

**Format the response as JSON with this exact structure (matching Flutter app expectations):**
{
  "days": [
    {
      "total_nutrition": {
        "calories": 2000,
        "protein": 150.0,
        "carbs": 200.0,
        "fat": 65.0,
        "fiber": 30.0
      },
      "meals": [
        {
          "name": "meal name",
          "description": "Brief meal description",
          "type": "breakfast",
          "ingredients": [
            {
              "name": "ingredient name with quantity",
              "needs_preparation": true,
              "instructions": ["step 1", "step 2", "step 3"]
            },
            {
              "name": "ready-to-eat ingredient name with quantity",
              "needs_preparation": false,
              "instructions": []
            }
          ],
          "nutrition": {
            "calories": 400,
            "protein": 20.0,
            "carbs": 45.0,
            "fat": 15.0,
            "fiber": 8.0
          },
          "preparation_time": 15,
          "difficulty": "easy"
        }
      ]
    }
  ],
  "raw_response": "Generated meal plan based on user preferences"
}

**CRITICAL INSTRUCTIONS:**
1. RESPOND WITH VALID JSON ONLY - NO OTHER TEXT
2. Use meal type names: "breakfast", "lunch", "dinner", "snack"
3. All nutrition values must be numbers: calories as integer, protein/carbs/fat/fiber as decimals with .0
4. preparation_time should be in minutes (number)
5. difficulty should be one of: "easy", "medium", "hard"
6. Include ${mealsPerDay} main meals and ${snacksPerDay} snacks per day
7. Ensure total daily nutrition aligns with the specified goals
8. DO NOT include any explanatory text, markdown formatting, or code blocks
9. START your response directly with the opening { bracket
10. IMPORTANT: Use decimal format for protein, carbs, fat, fiber (e.g., 20.0, not 20)
11. **INGREDIENT PREPARATION RULES:**
    - For ingredients needing cooking/preparation: needs_preparation: true, instructions: ["step1", "step2"]
    - For ready-to-eat ingredients: needs_preparation: false, instructions: []
    - Examples of ingredients needing preparation: raw meat, rice, pasta, vegetables for cooking
    - Examples of ready-to-eat ingredients: fruits, nuts, yogurt, cheese, bread, pre-cooked items
12. All ingredients MUST include both needs_preparation (boolean) and instructions (array) fields
`;
}

/**
 * Creates a nutrition analysis prompt for OpenAI
 * @param {string[]} foodItems - Array of food items to analyze
 * @param {string} portion - Portion size description
 * @returns {string} Formatted prompt for OpenAI
 */
function createNutritionAnalysisPrompt(foodItems, portion) {
  return `
Analyze the nutritional content of the following food items for ${portion}:

Food items: ${foodItems.join(', ')}

Provide a detailed nutritional breakdown in JSON format:
{
  "totalNutrition": {
    "calories": 0,
    "protein": 0,
    "carbs": 0,
    "fat": 0,
    "fiber": 0,
    "sugar": 0,
    "sodium": 0,
    "cholesterol": 0,
    "vitaminC": 0,
    "calcium": 0,
    "iron": 0
  },
  "breakdown": [
    {
      "item": "food item name",
      "nutrition": {
        "calories": 0,
        "protein": 0,
        "carbs": 0,
        "fat": 0,
        "fiber": 0
      }
    }
  ],
  "healthScore": 85,
  "recommendations": ["recommendation1", "recommendation2"]
}
`;
}

/**
 * Creates a shopping list generation prompt for OpenAI
 * @param {Array} mealsData - Array of meal objects with ingredients
 * @param {Date} startDate - Start date of the meal plan
 * @param {Date} endDate - End date of the meal plan
 * @param {string} preferredLanguage - User's preferred language ('ar' for Arabic, 'en' for English)
 * @returns {string} Formatted prompt for OpenAI
 */
function createShoppingListPrompt(mealsData, startDate, endDate, preferredLanguage) {
  // Format date range for title
  const formatDate = (date) => {
    return `${date.getDate()}/${date.getMonth() + 1}`;
  };

  const dateRangeText = `${formatDate(startDate)} - ${formatDate(endDate)}`;
  const isArabic = preferredLanguage === 'ar';

  // Prepare meals context for OpenAI
  const mealsContext = mealsData.map(meal => {
    const ingredientsText = meal.ingredients.map(ing =>
      typeof ing === 'string' ? ing : ing.name || ing
    ).join(', ');

    return `${meal.name} (${meal.type}): ${ingredientsText}`;
  }).join('\n');

  return `You are a nutrition expert specialized in creating organized shopping lists. Based on the following meals, create a comprehensive and organized shopping list.

Meals:
${mealsContext}

Requirements:
1. Aggregate all ingredients and calculate required quantities
2. Categorize ingredients by type (vegetables, fruits, proteins, etc.)
3. Estimate quantities with appropriate units (kg, grams, pieces, bottles, etc.)
4. Use ${isArabic ? 'Arabic' : 'English'} names for ingredients and categories
5. Organize items logically for efficient shopping
6. IMPORTANT: Only include categories that have items - do not return empty categories

Return the result in JSON format with exactly this structure (without IDs - they will be added later):
{
  "title": "${isArabic ? `قائمة التسوق - ${dateRangeText}` : `Shopping List - ${dateRangeText}`}",
  "categories": [
    {
      "name": "category_name",
      "arabic_name": "${isArabic ? 'الاسم العربي للفئة' : 'Arabic category name'}",
      "items": [
        {
          "name": "${isArabic ? 'اسم المكون' : 'ingredient name'}",
          "quantity": "quantity",
          "unit": "${isArabic ? 'الوحدة' : 'unit'}"
        }
      ],
      "icon": "🥬"
    }
  ],
  "notes": "${isArabic ? 'قائمة تسوق تم إنشاؤها تلقائياً من خطة الوجبات' : 'Shopping list automatically generated from meal plan'}"
}

Use the following categories with appropriate icons (only include if they have items):
- vegetables (${isArabic ? 'خضروات' : 'vegetables'}) 🥬
- fruits (${isArabic ? 'فواكه' : 'fruits'}) 🍎
- proteins (${isArabic ? 'بروتينات' : 'proteins'}) 🥩
- dairy (${isArabic ? 'ألبان' : 'dairy'}) 🥛
- grains (${isArabic ? 'حبوب ونشويات' : 'grains and starches'}) 🌾
- spices (${isArabic ? 'توابل وبهارات' : 'spices and seasonings'}) 🧂
- oils (${isArabic ? 'زيوت ودهون' : 'oils and fats'}) 🫒
- beverages (${isArabic ? 'مشروبات' : 'beverages'}) 🥤
- canned (${isArabic ? 'معلبات' : 'canned goods'}) 🥫
- bakery (${isArabic ? 'مخبوزات' : 'bakery items'}) 🍞
- frozen (${isArabic ? 'مجمدات' : 'frozen foods'}) 🧊
- snacks (${isArabic ? 'وجبات خفيفة' : 'snacks'}) 🍿
- household (${isArabic ? 'منتجات منزلية' : 'household items'}) 🧽

Critical Instructions:
- Only include categories that contain actual items from the meals
- Do not create empty categories or placeholder categories
- If a category has no items, completely exclude it from the response
- Aggregate duplicate ingredients across meals and sum their quantities
- Use realistic quantities based on typical shopping needs
- Respond with valid JSON only - no other text before or after
- Start your response directly with the opening { bracket`;
}

/**
 * Create prompt for meal replacement
 * @param {Object} originalMeal - Original meal to replace
 * @param {Object} userProfile - User profile with preferences
 * @param {string} customIngredients - Optional custom ingredients
 * @returns {string} Formatted prompt for OpenAI
 */
function createMealReplacementPrompt(originalMeal, userProfile, customIngredients) {
  const basePrompt = `
Generate a replacement meal for: ${originalMeal.name}
Original meal type: ${originalMeal.type}
Target nutrition: ${originalMeal.nutrition.calories} calories, ${originalMeal.nutrition.protein}g protein, ${originalMeal.nutrition.carbs}g carbs, ${originalMeal.nutrition.fat}g fat

User preferences:
- Dietary restrictions: ${userProfile.dietaryRestrictions?.join(', ') || 'None'}
- Allergies: ${userProfile.allergies?.join(', ') || 'None'}
- Favorite cuisines: ${userProfile.favoriteCuisines?.join(', ') || 'Arabic'}
`;

  const customIngredientsPrompt = customIngredients ?
    `\nAvailable ingredients: ${customIngredients}\nPlease use these ingredients when possible.` : '';

  return basePrompt + customIngredientsPrompt + `

Generate a JSON response with this exact structure (no markdown, no code blocks):
{
  "name": "meal name in Arabic",
  "description": "brief description in Arabic",
  "type": "${originalMeal.type}",
  "ingredients": [
    {
      "name": "ingredient name with quantity",
      "needs_preparation": true,
      "instructions": ["step 1", "step 2", "step 3"]
    },
    {
      "name": "ready-to-eat ingredient name with quantity",
      "needs_preparation": false,
      "instructions": []
    }
  ],
  "nutrition": {
    "calories": 400,
    "protein": 20.0,
    "carbs": 45.0,
    "fat": 15.0,
    "fiber": 8.0
  },
  "preparation_time": 15,
  "difficulty": "easy"
}

Requirements for ingredients:
1. Include the quantity directly in the ingredient name (e.g., "1 كوب أرز بني", "200 جرام دجاج مشوي", "2 ملعقة كبيرة زيت زيتون")
2. Set "needs_preparation" to true if the ingredient requires cooking/preparation, false if ready-to-eat
3. If needs_preparation is true, provide step-by-step preparation instructions in Arabic
4. Instructions should cover washing, cutting, cooking, seasoning as needed
5. For ready-to-eat items, set needs_preparation to false and instructions to empty array []
6. Use common Arabic measurements and cooking terms

Important: Return only the JSON object, nothing else.`;
}

module.exports = {
  createMealPlanPrompt,
  createNutritionAnalysisPrompt,
  createShoppingListPrompt,
  createMealReplacementPrompt,
};
