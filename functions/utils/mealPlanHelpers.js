const crypto = require('crypto');
const {Timestamp} = require('firebase-admin/firestore');

/**
 * Generate unique hash-based ID for a meal
 * @param {string} userId - User ID
 * @param {Date} date - Meal date
 * @param {number} mealIndex - Index of meal in the day
 * @param {string} mealName - Name of the meal
 * @returns {string} Unique meal ID
 */
function generateMealId(userId, date, mealIndex, mealName) {
  const hashInput = `${userId}_${date.toISOString().split('T')[0]}_${mealIndex}_${mealName}_${Date.now()}`;
  const hash = crypto.createHash('sha256').update(hashInput).digest('hex').substring(0, 16);
  return `meal_${hash}`;
}

/**
 * Generate unique hash-based ID for a meal replacement
 * @param {string} userId - User ID
 * @param {string} dayDocumentId - Day document ID
 * @param {string} mealId - Original meal ID
 * @returns {string} Unique replacement ID
 */
function generateReplacementId(userId, dayDocumentId, mealId) {
  const hashInput = `${userId}_${dayDocumentId}_${mealId}_${Date.now()}_replacement`;
  const hash = crypto.createHash('sha256').update(hashInput).digest('hex').substring(0, 16);
  return `replacement_${hash}`;
}

/**
 * Validate meal replacement limits
 * @param {Object} meal - Meal object to check
 * @returns {Object} Validation result
 */
function validateReplacementLimits(meal) {
  const replacementCount = meal.replacement_count || 0;
  
  if (replacementCount >= 3) {
    return {
      isValid: false,
      error: 'Maximum replacement limit reached (3)',
      code: 'REPLACEMENT_LIMIT_EXCEEDED'
    };
  }

  if (meal.is_consumed) {
    return {
      isValid: false,
      error: 'Cannot replace consumed meal',
      code: 'MEAL_ALREADY_CONSUMED'
    };
  }

  return {
    isValid: true
  };
}

/**
 * Create replacement record structure
 * @param {string} replacementId - Unique replacement ID
 * @param {Object} replacementMeal - Generated replacement meal
 * @param {string} reason - Reason for replacement
 * @returns {Object} Replacement record
 */
function createReplacementRecord(replacementId, replacementMeal, reason = 'user_request') {
  return {
    id: replacementId,
    replaced_at: Timestamp.now(),
    reason: reason,
    meal: replacementMeal,
    is_original: false,
  };
}

/**
 * Create original meal record for replacement history
 * @param {Object} originalMeal - Original meal data
 * @returns {Object} Original meal record
 */
function createOriginalMealRecord(originalMeal) {
  // Exclude root-only fields from the original meal copy
  const {
    is_consumed,
    consumed_at,
    type,
    replacement_history,
    replacement_count,
    selected_meal_id,
    ...originalMealCopy
  } = originalMeal;

  // Ensure the original meal copy has required fields for deserialization
  const cleanOriginalMealCopy = {
    ...originalMealCopy,
    name: originalMealCopy.name || 'وجبة أصلية',
    ingredients: originalMealCopy.ingredients || [],
    nutrition: originalMealCopy.nutrition || {
      calories: 0,
      protein: 0.0,
      carbs: 0.0,
      fat: 0.0,
      fiber: 0.0,
    },
  };

  return {
    id: 'original',
    replaced_at: Timestamp.now(),
    reason: 'original',
    meal: cleanOriginalMealCopy,
    is_original: true,
  };
}

/**
 * Update meal with replacement history
 * @param {Object} currentMeal - Current meal object
 * @param {Object} replacement - New replacement record
 * @returns {Object} Updated meal object
 */
function updateMealWithReplacement(currentMeal, replacement) {
  let updatedReplacementHistory;
  const replacementCount = currentMeal.replacement_count || 0;

  if (!currentMeal.replacement_history || currentMeal.replacement_history.length === 0) {
    // First replacement: copy current meal as original, then add new replacement
    const originalReplacement = createOriginalMealRecord(currentMeal);
    updatedReplacementHistory = [originalReplacement, replacement];
  } else {
    // Add to existing replacement history
    updatedReplacementHistory = [...currentMeal.replacement_history, replacement];
  }

  return {
    ...currentMeal,
    replacement_history: updatedReplacementHistory,
    replacement_count: replacementCount + 1,
  };
}

/**
 * Validate meal structure for replacement
 * @param {Object} mealData - Parsed meal data from OpenAI
 * @param {Object} originalMeal - Original meal for fallback nutrition
 * @returns {Object} Validated meal structure
 */
function validateReplacementMealStructure(mealData, originalMeal) {
  if (!mealData || typeof mealData !== 'object') {
    throw new Error('Invalid meal data structure');
  }

  return {
    name: mealData.name || 'وجبة بديلة',
    ingredients: Array.isArray(mealData.ingredients) ? mealData.ingredients : [],
    nutrition: (mealData.nutrition && typeof mealData.nutrition === 'object')
      ? mealData.nutrition
      : originalMeal.nutrition,
    description: mealData.description || '',
    preparation_time: Number(mealData.preparation_time || mealData.preparationTime) || 30,
    difficulty: mealData.difficulty || 'easy',
  };
}

/**
 * Clean OpenAI response text to extract JSON
 * @param {string} responseText - Raw response from OpenAI
 * @returns {string} Cleaned JSON text
 */
function cleanOpenAIResponse(responseText) {
  let cleanedText = responseText.trim();

  // Remove markdown code blocks if present
  if (cleanedText.startsWith('```json')) {
    cleanedText = cleanedText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
  } else if (cleanedText.startsWith('```')) {
    cleanedText = cleanedText.replace(/^```\s*/, '').replace(/\s*```$/, '');
  }

  // Remove any leading/trailing backticks
  if (cleanedText.match(/^`+|`+$/)) {
    cleanedText = cleanedText.replace(/^`+|`+$/g, '');
  }

  return cleanedText;
}

/**
 * Format date range for notifications
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date (optional)
 * @param {string} preferredLanguage - User's preferred language
 * @returns {string} Formatted date range
 */
function formatDateRangeForNotification(startDate, endDate = null, preferredLanguage = 'ar') {
  const options = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };

  if (preferredLanguage === 'en') {
    const startFormatted = startDate.toLocaleDateString('en-US', options);
    if (endDate) {
      const endFormatted = endDate.toLocaleDateString('en-US', options);
      return `${startFormatted} - ${endFormatted}`;
    }
    return startFormatted;
  } else {
    // Arabic formatting
    const startFormatted = startDate.toLocaleDateString('ar-SA', options);
    if (endDate) {
      const endFormatted = endDate.toLocaleDateString('ar-SA', options);
      return `${startFormatted} - ${endFormatted}`;
    }
    return startFormatted;
  }
}

/**
 * Get notification content for meal plan generation
 * @param {string} preferredLanguage - User's preferred language
 * @param {number} daysCount - Number of days in meal plan
 * @param {string} formattedDate - Formatted start date
 * @returns {Object} Notification content
 */
function getMealPlanNotificationContent(preferredLanguage, daysCount, formattedDate) {
  if (preferredLanguage === 'en') {
    return {
      title: 'Meal Plan Ready',
      message: `Your new ${daysCount}-day meal plan is ready! Starting from ${formattedDate}. Tap to view your meals.`,
    };
  } else {
    // Default to Arabic
    return {
      title: 'خطة الوجبات جاهزة',
      message: `خطة الوجبات الجديدة لمدة ${daysCount} أيام جاهزة! تبدأ من ${formattedDate}. اضغط لعرض وجباتك.`,
    };
  }
}

/**
 * Get notification content for meal replacement
 * @param {string} preferredLanguage - User's preferred language
 * @param {string} replacementMealName - Name of replacement meal
 * @returns {Object} Notification content
 */
function getMealReplacementNotificationContent(preferredLanguage, replacementMealName) {
  if (preferredLanguage === 'en') {
    return {
      title: 'Meal Replacement Ready',
      message: `Your new meal replacement "${replacementMealName}" is ready! Tap to view your meal options.`,
    };
  } else {
    // Default to Arabic
    return {
      title: 'بديل الوجبة جاهز',
      message: `بديل الوجبة الجديد "${replacementMealName}" جاهز! اضغط لعرض خيارات الوجبة.`,
    };
  }
}

module.exports = {
  generateMealId,
  generateReplacementId,
  validateReplacementLimits,
  createReplacementRecord,
  createOriginalMealRecord,
  updateMealWithReplacement,
  validateReplacementMealStructure,
  cleanOpenAIResponse,
  formatDateRangeForNotification,
  getMealPlanNotificationContent,
  getMealReplacementNotificationContent,
};
