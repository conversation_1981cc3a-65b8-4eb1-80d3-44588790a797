const {initializeFirebase} = require('../config/firebase');
const {Timestamp} = require('firebase-admin/firestore');

/**
 * Generate a Firestore auto-generated document ID
 * @param {string} collectionPath - Optional collection path for context
 * @return {string} Auto-generated Firestore document ID
 */
function generateFirestoreDocumentId(collectionPath = 'temp') {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Use Firestore's auto-generated ID mechanism
    const docRef = db.collection(collectionPath).doc();
    return docRef.id;
  } catch (error) {
    console.error('Error generating Firestore document ID:', error);
    // Fallback to timestamp-based ID if Firestore fails
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Generate a document ID for a user's subcollection
 * @param {string} userId - User ID
 * @param {string} subcollectionName - Subcollection name
 * @return {string} Auto-generated document ID
 */
function generateUserSubcollectionDocumentId(userId, subcollectionName) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Generate ID using the actual collection path
    const collectionRef = db
      .collection('users')
      .doc(userId)
      .collection(subcollectionName);

    return collectionRef.doc().id;
  } catch (error) {
    console.error('Error generating user subcollection document ID:', error);
    // Fallback to generic document ID
    return generateFirestoreDocumentId();
  }
}

/**
 * Generate a document reference for a user's subcollection
 * @param {string} userId - User ID
 * @param {string} subcollectionName - Subcollection name
 * @param {string} documentId - Optional specific document ID
 * @return {Object} Firestore document reference
 */
function getUserSubcollectionDocumentRef(userId, subcollectionName, documentId = null) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    const collectionRef = db
      .collection('users')
      .doc(userId)
      .collection(subcollectionName);

    // Use provided ID or auto-generate
    return documentId ? collectionRef.doc(documentId) : collectionRef.doc();
  } catch (error) {
    console.error('Error creating user subcollection document reference:', error);
    throw error;
  }
}



module.exports = {
  generateUserSubcollectionDocumentId,
};
